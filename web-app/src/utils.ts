import { SvgIconTypeMap } from '@mui/material';
import { OverridableComponent } from '@mui/types';
import { SerializedError } from '@reduxjs/toolkit';
import { FetchBaseQueryError } from '@reduxjs/toolkit/dist/query';
import dayjs from 'dayjs';
import { KeyboardEvent } from 'react';
import * as yup from 'yup';
import isoWeek from 'dayjs/plugin/isoWeek';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { EnumMetaItem, EnumMetaMap } from './types';

// Configure dayjs for week and timezone calculations
dayjs.extend(isoWeek);
dayjs.extend(timezone);
dayjs.extend(utc);

export function getCookie(name: string) {
  if (!document.cookie) {
    return null;
  }

  const xsrfCookies = document.cookie
    .split(';')
    .map((c) => c.trim())
    .filter((c) => c.startsWith(`${name}=`));

  if (xsrfCookies.length === 0) {
    return null;
  }
  return decodeURIComponent(xsrfCookies[0].split('=')[1]);
}

export function getErrorStatus(error: FetchBaseQueryError | SerializedError): number {
  if (!error) {
    return -1;
  }
  const statusError = error as unknown as FetchBaseQueryError;
  return typeof statusError?.status === 'number' ? statusError?.status : -1;
}

export function getDatePlusTimeString(date: Date) {
  const datePlusDay = date.toDateString();
  const hours = date.getHours();
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${datePlusDay} ${hours}:${minutes}`;
}

export function getTimeString(date: Date) {
  const hours = date.getHours();
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${hours}:${minutes}`;
}

/**
 * Converts a date to a string representation of its ISO week number and year
 * @param date - The date to convert, can be either a Date object or an ISO date string
 * @returns A string in the format "Week X, YYYY" where X is the ISO week number and YYYY is the year
 * @example
 * getWeekNumberAndYear(new Date('2023-01-01')) // Returns "Week 52, 2022"
 * getWeekNumberAndYear('2023-12-31') // Returns "Week 52, 2023"
 * getWeekNumberAndYear('invalid') // Returns "Invalid Date"
 */
export function getWeekNumberAndYear(date: Date | string) {
  // Get user's timezone
  const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  // Parse the ISO date string and convert to user's timezone
  const parsedDate = dayjs(date).tz(userTimeZone);

  // Check if the date is valid
  if (!parsedDate.isValid()) {
    return 'Invalid Date';
  }

  // Get ISO week number and year (use isoWeekYear for correct year calculation)
  const weekNumber = parsedDate.isoWeek();
  const year = parsedDate.isoWeekYear();

  return `Week ${weekNumber}, ${year}`;
}

export function sortDates(a: string, b: string) {
  const dateA = dayjs(a);
  const dateB = dayjs(b);
  return dateA.isBefore(dateB) ? -1 : 1;
}

export interface PaginatedResult<T> {
  total: number;
  pageNumber: number;
  pageSize: number;
  hasNext: boolean;
  hasPrev: boolean;
  content: T[];
}

export function preventSubmitOnEnter(e: KeyboardEvent) {
  const isTextArea = e.target instanceof HTMLTextAreaElement;
  if (e.key === 'Enter' && !isTextArea) {
    // Keeps form from submiting on hitting enter
    e.preventDefault();
  }
}

export interface ApiError {
  title: string;
  status: number;
  detail: string;
  stacktrace: string[];
}

export function getApiError(error: FetchBaseQueryError): ApiError {
  return (error.data as ApiError) ?? { title: '', status: -1, detail: '', stacktrace: [] };
}

export function isValidDayjs(value: unknown, context: yup.TestContext): boolean | yup.ValidationError {
  if (dayjs.isDayjs(value) && !value.isValid()) {
    // eslint-disable-next-line no-template-curly-in-string
    return context.createError({ message: '${label} should be a valid date' });
  }
  return true;
}

export function isPastDayjs(value: unknown, context: yup.TestContext): boolean | yup.ValidationError {
  if (dayjs.isDayjs(value) && value.isAfter(dayjs())) {
    // eslint-disable-next-line no-template-curly-in-string
    return context.createError({ message: '${label} should be in the past' });
  }
  return true;
}

export type MUIIcon = OverridableComponent<SvgIconTypeMap<unknown, 'svg'>> & { muiName: string };

/**
 * Creates a record mapping enum values to themselves
 * @param e - Record containing enum values as values
 * @returns A record where both keys and values are the enum values
 * @example
 * const MyEnum = { FOO: 'FOO', BAR: 'BAR' } as const;
 * mapEnumValues(MyEnum) // Returns { FOO: 'FOO', BAR: 'BAR' }
 */
export function mapEnumValues<E extends string>(e: Record<string, E>) {
  return (Object.values(e) as E[]).reduce((acc, v) => {
    acc[v] = v;
    return acc;
  }, {} as Record<E, E>);
}

/**
 * Builds a fully-featured dynamic EnumMetaMap:
 *  • `get`          – on-demand meta for any string key
 *  • `has`          – `"key" in map` is always true for strings
 *  • `ownKeys`      – returns an empty array (non-iterable by default)
 *  • `getOwnPropertyDescriptor` – lets tools that rely on descriptors still work
 *
 * Even if some traps feel overkill, wrapping them in a factory keeps the surface area small
 * and guarantees every dynamic map behaves exactly the same.
 */
export function createDynamicMetaMap<K extends string, V extends EnumMetaItem>(
  buildMeta: (key: K) => V
): EnumMetaMap<K> {
  // The empty object gets its type coerced, then wrapped.
  return new Proxy({} as EnumMetaMap<K>, {
    // Main lookup
    get(_target, prop: string | symbol) {
      return typeof prop === 'string' ? buildMeta(prop as K) : undefined;
    },

    // `"foo" in obj` – always true for strings so nothing breaks
    has(_target, prop) {
      return typeof prop === 'string';
    },

    // Enumeration (Object.keys, for…in) – intentionally empty to avoid huge key lists
    ownKeys() {
      return [];
    },

    // Allows frameworks that call Object.getOwnPropertyDescriptor internally
    getOwnPropertyDescriptor(_target, prop) {
      if (typeof prop === 'string') {
        return {
          enumerable: true,
          configurable: true,
          value: buildMeta(prop as K),
        };
      }
      return undefined;
    },
  });
}
