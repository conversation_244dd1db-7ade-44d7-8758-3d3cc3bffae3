import { useMemo } from 'react';
import { ResponsiveHeatMap } from '@nivo/heatmap';
import { Box, Typography } from '@mui/material';
import { truncate } from '../chartFunctions';
import useAxisMargin, { AxisOrientation } from '../hooks/useAxisMargin';
import { ChartFields, isChartFieldWithStacks } from '../chartTypes';

interface PivotTableProps {
  data: ChartFields;
}
interface PivotData {
  rows: string[];
  columns: string[];
  values: Record<string, Record<string, number>>;
  totals: {
    rowTotals: Record<string, number>;
    columnTotals: Record<string, number>;
    grandTotal: number;
  };
  maxValue: number;
}

function PivotTable({ data }: PivotTableProps) {
  const defaultMargin = 8;
  const topMargin = defaultMargin;
  const rightMargin = defaultMargin + 5;
  const truncateLabelsAt = 25;

  const pivotData = useMemo((): PivotData => {
    let longest: ChartFields = new Map();
    let current = 0;

    data.entries().forEach(([, rowValue]) => {
      if (isChartFieldWithStacks(rowValue) && current < rowValue.stacks.size) {
        current = rowValue.stacks.size;
        longest = rowValue.stacks;
      }
    });

    const rows: string[] = [];
    const values: Record<string, Record<string, number>> = {};
    const rowTotals: Record<string, number> = {};
    const columnTotals: Record<string, number> = {};
    let grandTotal = 0;
    let maxValue = 0;

    data.entries().forEach(([rowKey, rowValue]) => {
      const rowLabel = rowValue.label ?? rowKey;
      if (isChartFieldWithStacks(rowValue)) {
        rows.push(rowLabel);
        values[rowLabel] = {};
        rowTotals[rowLabel] = 0;

        rowValue.stacks
          .entries()
          .toArray()
          .forEach(([colKey, colValue]) => {
            const colLabel = colValue.label ?? colKey;
            const { count } = colValue;
            values[rowLabel][colLabel] = count;
            rowTotals[rowLabel] += count;
            columnTotals[colLabel] = (columnTotals[colLabel] || 0) + count;
            grandTotal += count;
            maxValue = Math.max(maxValue, count);
          });
      }
    });

    return {
      rows,
      columns: longest
        .keys()
        .map((key) => longest.get(key)?.label ?? key)
        .toArray(),
      values,
      totals: { rowTotals, columnTotals, grandTotal },
      maxValue,
    };
  }, [data]);

  const longestRow = useMemo(() => {
    const keys = pivotData.rows.map((row) => row);
    return keys.reduce(
      (a, b) => (truncate(a, truncateLabelsAt).length > truncate(b, truncateLabelsAt).length ? a : b),
      ''
    );
  }, [pivotData.rows]);

  const longestColumn = useMemo(() => {
    const values = pivotData.columns.map((col) => col);
    return values.reduce(
      (a, b) => (truncate(a, truncateLabelsAt).length > truncate(b, truncateLabelsAt).length ? a : b),
      ''
    );
  }, [pivotData.columns]);

  const heatmapData = useMemo(
    () =>
      pivotData.rows.map((row) => ({
        id: row,
        data: pivotData.columns.map((col) => ({
          x: col,
          y: pivotData.values[row]?.[col] || 0,
        })),
      })),
    [pivotData]
  );

  const {
    margin: bottomMargin,
    axisRef: bottomAxisRef,
    adjustedAxisOptions: bottomAxisOptions,
  } = useAxisMargin({
    axisOptions: {
      tickRotation: -45,
      truncateTickAt: truncateLabelsAt,
    },
    orientation: AxisOrientation.HORIZONTAL,
    axisDefaultMargin: defaultMargin,
  });
  const {
    margin: leftMargin,
    axisRef: leftAxisRef,
    adjustedAxisOptions: leftAxisOptions,
  } = useAxisMargin({
    axisOptions: {
      truncateTickAt: truncateLabelsAt,
    },
    orientation: AxisOrientation.VERTICAL,
    axisDefaultMargin: defaultMargin,
  });

  if (pivotData.rows.length === 0 || pivotData.columns.length === 0) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography color="text.secondary">No data available for pivot table</Typography>
      </Box>
    );
  }

  return (
    <>
      <span
        ref={leftAxisRef}
        style={{
          position: 'absolute',
          visibility: 'hidden',
          pointerEvents: 'none',
          whiteSpace: 'pre',
          fontFamily: 'sans-serif',
          fontSize: '11px',
          fontWeight: 400,
        }}
      >
        {truncate(longestRow, truncateLabelsAt)}
      </span>
      <span
        ref={bottomAxisRef}
        style={{
          position: 'absolute',
          visibility: 'hidden',
          pointerEvents: 'none',
          whiteSpace: 'pre',
          fontFamily: 'sans-serif',
          fontSize: '11px',
          fontWeight: 400,
        }}
      >
        {truncate(longestColumn, truncateLabelsAt)}
      </span>
      <Box sx={{ height: '100%', position: 'relative' }}>
        <ResponsiveHeatMap
          data={heatmapData}
          margin={{ top: topMargin, right: rightMargin, bottom: bottomMargin, left: leftMargin }}
          axisTop={null}
          axisRight={null}
          axisBottom={bottomAxisOptions}
          axisLeft={leftAxisOptions}
          animate
          motionConfig="wobbly"
          colors={{ type: 'diverging', scheme: 'orange_red' }}
        />
      </Box>
    </>
  );
}

export default PivotTable;
