import React, { useMemo } from 'react';
import { BarDatumWithColor, ResponsiveBar } from '@nivo/bar';
import { StatsModuleDisplayMap, StatsModule } from '../../stats/statsTypes';
import useAxisMargin, { AxisOrientation } from '../hooks/useAxisMargin';
import { getColors, getLabels, truncate } from '../chartFunctions';
import { ChartFields, ChartOrientation, isChartField, isChartFieldWithStacks } from '../chartTypes';
import useLongestTick from '../hooks/useLongestTick';
import { themeToColor } from '../../../theme';

interface BarChartProps {
  data: ChartFields;
  module: StatsModule;
  orientation?: ChartOrientation;
  stackedBy?: boolean;
}

function BarChart({ data, module, orientation = ChartOrientation.VERTICAL, stackedBy }: BarChartProps) {
  const isHorizontal = orientation === ChartOrientation.HORIZONTAL;
  const defaultMargin = 8;
  const topMargin = defaultMargin;
  const rightMargin = isHorizontal ? defaultMargin + 5 : defaultMargin;
  const truncateLabelsAt = 25;

  const barData = useMemo(() => {
    if (!stackedBy) {
      return data
        .entries()
        .map(([key, val]) => ({
          Group: val.label ?? key,
          [StatsModuleDisplayMap[module]]: isChartField(val) ? val.count : 0,
        }))
        .toArray();
    }
    return data
      .entries()
      .map(([displayGroup, primaryVal]) => {
        const row: Record<string, number | string> = { Group: primaryVal.label ?? displayGroup };

        if (!isChartFieldWithStacks(primaryVal)) {
          return row;
        }

        primaryVal.stacks?.entries().forEach(([keyStack, stackVal]) => {
          row[stackVal.label ?? keyStack] = stackVal.count;
        });
        return row;
      })
      .toArray();
  }, [data, module, stackedBy]);

  const keyLabelStackedMap = useMemo(() => getLabels(data, true), [data]);
  const keyLabelMap = useMemo(() => getLabels(data, false), [data]);

  const longestKey = useMemo(
    () =>
      keyLabelMap
        .values()
        .reduce((a, b) => (truncate(a, truncateLabelsAt).length > truncate(b, truncateLabelsAt).length ? a : b), ''),
    [keyLabelMap]
  );

  const longestValue = useLongestTick(
    !stackedBy
      ? Math.max(...data.values().map((val) => (isChartField(val) ? val.count : 0)))
      : Math.max(
          ...data.values().map((primVal) =>
            isChartFieldWithStacks(primVal)
              ? primVal.stacks
                  ?.values()
                  .toArray()
                  .reduce((sum, { count }) => sum + (count || 0), 0) || 0
              : 0
          )
        )
  );

  const fieldColorMapping = useMemo(() => {
    const base = getColors(data, !!stackedBy);
    return Object.fromEntries(keyLabelStackedMap.entries().map(([key, label]) => [label, base.get(key) ?? '']));
  }, [data, stackedBy, keyLabelStackedMap]);

  const {
    margin: bottomMargin,
    axisRef: bottomAxisRef,
    adjustedAxisOptions: bottomAxisOptions,
  } = useAxisMargin({
    axisOptions: {
      tickRotation: isHorizontal ? 0 : -45,
      truncateTickAt: isHorizontal ? 0 : truncateLabelsAt,
    },
    orientation: AxisOrientation.HORIZONTAL,
    axisDefaultMargin: defaultMargin,
  });

  const {
    margin: leftMargin,
    axisRef: leftAxisRef,
    adjustedAxisOptions: leftAxisOptions,
  } = useAxisMargin({
    axisOptions: {
      truncateTickAt: isHorizontal ? truncateLabelsAt : 0,
    },
    orientation: AxisOrientation.VERTICAL,
    axisDefaultMargin: defaultMargin,
  });

  return (
    <>
      <span
        ref={leftAxisRef}
        style={{
          position: 'absolute',
          visibility: 'hidden',
          pointerEvents: 'none',
          whiteSpace: 'pre',
          fontFamily: 'sans-serif',
          fontSize: '11px',
          fontWeight: 400,
        }}
      >
        {isHorizontal ? truncate(longestKey, truncateLabelsAt) : longestValue}
      </span>
      <span
        ref={bottomAxisRef}
        style={{
          position: 'absolute',
          visibility: 'hidden',
          pointerEvents: 'none',
          whiteSpace: 'pre',
          fontFamily: 'sans-serif',
          fontSize: '11px',
          fontWeight: 400,
        }}
      >
        {isHorizontal ? longestValue : truncate(longestKey, truncateLabelsAt)}
      </span>

      <ResponsiveBar
        data={barData as BarDatumWithColor[]}
        keys={stackedBy ? keyLabelStackedMap.values().toArray() : [StatsModuleDisplayMap[module]]}
        indexBy="Group"
        margin={{
          top: topMargin,
          right: rightMargin,
          bottom: bottomMargin,
          left: leftMargin,
        }}
        padding={0.3}
        layout={isHorizontal ? 'horizontal' : 'vertical'}
        axisBottom={bottomAxisOptions}
        axisLeft={leftAxisOptions}
        colors={
          stackedBy
            ? ({ id }) => fieldColorMapping[String(id)] ?? themeToColor('primary.main')
            : () => themeToColor('primary.main')
        }
        enableLabel={false}
        animate
        motionConfig="gentle"
      />
    </>
  );
}

export default BarChart;
