import { useMemo } from 'react';
import { ResponsiveLine } from '@nivo/line';
import { ResponsiveStream, StreamDatum } from '@nivo/stream';
import dayjs from 'dayjs';
import { generateCompleteTimeSeries } from '../../stats/statsFunctions';
import useAxisMargin, { AxisOrientation } from '../hooks/useAxisMargin';
import { WeekTotalCount } from '../../stats/statsTypes';
import { chartFieldsToTimeSeries, extractY, getColors, getLabels, truncate } from '../chartFunctions';
import useLongestTick from '../hooks/useLongestTick';
import { themeToColor } from '../../../theme';
import { ChartFields } from '../chartTypes';

interface LineChartProps {
  data: ChartFields;
  startDate: number;
  endDate: number;
  stackedBy?: boolean;
}

const hiddenSpanStyle = {
  position: 'absolute' as const,
  visibility: 'hidden' as const,
  pointerEvents: 'none' as const,
  whiteSpace: 'pre' as const,
  fontFamily: 'sans-serif',
  fontSize: '11px',
  fontWeight: 400,
};

// ? Helper to remove duplicate x-values (keeping first occurrence), this is a workaround our flawed WeektTotal[] because of possible duplicate weeks.
const uniqueByX = <T extends { x: number }>(data: T[]) => {
  const seen = new Set<number>();
  return data.filter((item) => {
    if (seen.has(item.x)) return false;
    seen.add(item.x);
    return true;
  });
};

function LineChart({ data, startDate, endDate, stackedBy }: LineChartProps) {
  const axisLegendText = 'Weeks';
  const defaultMargin = 10;
  const truncateLabelsAt = 25;
  const {
    margin: bottomMargin,
    axisRef: bottomAxisRef,
    legendRef: bottomAxisLegendRef,
    adjustedAxisOptions: bottomAxisOptions,
  } = useAxisMargin({
    axisOptions: {
      legend: axisLegendText,
      legendOffset: 5,
      legendPosition: 'middle',
      truncateTickAt: 0,
    },
    orientation: AxisOrientation.HORIZONTAL,
    axisDefaultMargin: defaultMargin,
  });

  const {
    margin: leftMargin,
    axisRef: leftAxisRef,
    adjustedAxisOptions: leftAxisOptions,
  } = useAxisMargin({
    axisOptions: {},
    orientation: AxisOrientation.VERTICAL,
    axisDefaultMargin: defaultMargin,
  });

  const timeSeriesData = useMemo(() => {
    const points = chartFieldsToTimeSeries(data);
    return generateCompleteTimeSeries(
      points,
      startDate || dayjs().subtract(6, 'months').valueOf(),
      endDate || dayjs().valueOf()
    );
  }, [data, startDate, endDate]);

  const labels = useMemo(() => getLabels(data, !!stackedBy), [data, stackedBy]);

  const colors = useMemo(() => getColors(data, !!stackedBy), [data, stackedBy]);

  const lineData = useMemo(() => {
    const label = 'total';
    const color = themeToColor('primary.main');
    const raw = timeSeriesData.map(({ week, total }) => ({
      x: week,
      y: extractY(total, String(week)),
    }));
    return [{ id: label, color, data: uniqueByX(raw) }];
  }, [timeSeriesData]);

  const streamData = useMemo(() => {
    const raw: (StreamDatum & { x: number })[] = timeSeriesData.map((pt: WeekTotalCount) => {
      const obj: StreamDatum & { x: number } = { x: pt.week };
      getLabels(data, !!stackedBy)
        .entries()
        .forEach(([key, label]) => {
          obj[label] = extractY(pt.total, key);
        });
      return obj;
    });
    const sorted = raw.sort((a, b) => a.x - b.x);
    return uniqueByX(sorted);
  }, [data, timeSeriesData, stackedBy]);

  const longestLabel = useMemo(
    () =>
      labels
        .values()
        .reduce((a, b) => (truncate(a, truncateLabelsAt).length > truncate(b, truncateLabelsAt).length ? a : b), ''),
    [labels, truncateLabelsAt]
  );

  const longestValue = useLongestTick(
    Math.max(
      ...timeSeriesData.flatMap((p) =>
        labels
          .keys()
          .toArray()
          .map((k) => extractY(p.total, k))
      )
    )
  );

  return (
    <>
      <span ref={leftAxisRef} style={hiddenSpanStyle}>
        {longestValue}
      </span>
      <span ref={bottomAxisRef} style={hiddenSpanStyle}>
        {longestLabel}
      </span>
      <span ref={bottomAxisLegendRef} style={hiddenSpanStyle}>
        {axisLegendText}
      </span>

      {stackedBy ? (
        <ResponsiveStream
          data={streamData}
          keys={labels.keys().toArray()}
          margin={{ top: defaultMargin, right: defaultMargin + 5, bottom: bottomMargin, left: leftMargin }}
          curve="monotoneX"
          axisBottom={bottomAxisOptions}
          axisLeft={leftAxisOptions}
          colors={({ id }) => colors.get(String(id)) ?? themeToColor('primary.main')}
          offsetType="none"
          enableGridX={false}
        />
      ) : (
        <ResponsiveLine
          data={lineData}
          margin={{ top: defaultMargin, right: defaultMargin + 5, bottom: bottomMargin, left: leftMargin }}
          curve="monotoneX"
          xScale={{ type: 'point' }}
          yScale={{ type: 'linear', min: 0, max: 'auto' }}
          axisBottom={bottomAxisOptions}
          axisLeft={leftAxisOptions}
          enableGridX={false}
          useMesh
          colors={({ color }) => color}
        />
      )}
    </>
  );
}

export default LineChart;
