import { Accordion, AccordionDetails, AccordionSummary, Box, CircularProgress, Typography } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ColorPicker from './ColorPicker';

export interface ColorItem {
  id: string;
  label: string;
  color: string;
}

export interface ColorPickerAccordionProps {
  title: string;
  items: ColorItem[];
  onItemsChange: (items: ColorItem[]) => void;
  isLoading: boolean;
  expanded: boolean;
  setExpanded: (expanded: boolean) => void;
}

function ColorPickerAccordion({
  title,
  items,
  onItemsChange,
  isLoading,
  expanded,
  setExpanded,
}: ColorPickerAccordionProps) {
  return (
    <Accordion
      expanded={expanded}
      onChange={(_, isExpanded) => setExpanded(isExpanded)}
      elevation={0}
      sx={{ '&:before': { display: 'none' }, '&.Mui-expanded': { margin: 0 } }}
    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        disabled={isLoading}
        sx={{ p: 0, '& .MuiAccordionSummary-content': { m: 0 } }}
      >
        <Typography>{title}</Typography>
      </AccordionSummary>
      <AccordionDetails sx={{ p: 0 }}>
        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
            <CircularProgress size={24} />
          </Box>
        ) : (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, p: 1 }}>
            {items.length === 0 ? (
              <Typography variant="body2" color="text.secondary" align="center">
                No data points available
              </Typography>
            ) : (
              items.map(({ id, label, color }) => (
                <ColorPicker
                  key={id}
                  label={label}
                  color={color}
                  onChange={(newColor) => {
                    const updated = items.map((item) => (item.id === id ? { ...item, color: newColor } : item));
                    onItemsChange(updated);
                  }}
                />
              ))
            )}
          </Box>
        )}
      </AccordionDetails>
    </Accordion>
  );
}

export default ColorPickerAccordion;
