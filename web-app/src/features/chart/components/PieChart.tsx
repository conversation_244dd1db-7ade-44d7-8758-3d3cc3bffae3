import { useMemo } from 'react';
import { ResponsivePie, PieCustomLayerProps } from '@nivo/pie';
import { themeToColor } from '../../../theme';
import { getColors } from '../chartFunctions';
import { ChartFields, isChartField } from '../chartTypes';

interface PieChartProps {
  data: ChartFields;
}

function CenterTotalLayer({ centerX, centerY, total }: { centerX: number; centerY: number; total: number }) {
  return (
    <text
      x={centerX}
      y={centerY}
      textAnchor="middle"
      dominantBaseline="central"
      style={{
        fontSize: '16px',
        fontWeight: 'bold',
        fill: '#333',
      }}
    >
      {total}
    </text>
  );
}

interface PieDataItem {
  id: string;
  // ? Doesn't actually draw as label but needed because of typing of nivo.
  label: string;
  value: number;
  color: string;
}

function createCenterTotalLayer(total: number) {
  return function CenterLayer(props: PieCustomLayerProps<PieDataItem>) {
    const { centerX, centerY } = props;
    return <CenterTotalLayer centerX={centerX} centerY={centerY} total={total} />;
  };
}

function PieChart({ data }: PieChartProps) {
  const fieldColorMapping = useMemo(() => getColors(data, false), [data]);

  const pieData = useMemo<PieDataItem[]>(
    () =>
      data
        .entries()
        .map(([key, value]) => {
          let numericValue = 0;
          if (isChartField(value)) {
            numericValue = value.count as number;
          }
          return {
            id: value.label ?? key,
            label: value.label ?? key,
            value: numericValue,
            color: fieldColorMapping.get(key) ?? themeToColor('primary.main'),
          };
        })
        .toArray(),
    [data, fieldColorMapping]
  );

  const totalValue = useMemo(
    () =>
      data.values().reduce((sum, item) => {
        if (typeof item === 'object' && item !== null && 'count' in item) {
          return sum + (item.count as number);
        }
        return sum;
      }, 0),
    [data]
  );
  const centerTotalLayer = useMemo(() => createCenterTotalLayer(totalValue), [totalValue]);

  return (
    <ResponsivePie<PieDataItem>
      data={pieData}
      margin={{ top: 0, right: 20, bottom: 20, left: 20 }}
      innerRadius={0.5}
      padAngle={0.7}
      cornerRadius={3}
      colors={(datum) => datum.data.color}
      animate
      motionConfig="gentle"
      sortByValue
      enableArcLinkLabels={false}
      layers={['arcs', 'arcLabels', 'arcLinkLabels', 'legends', centerTotalLayer]}
    />
  );
}

export default PieChart;
