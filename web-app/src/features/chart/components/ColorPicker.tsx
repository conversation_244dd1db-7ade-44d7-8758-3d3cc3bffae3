import { Box, Select, MenuItem, Typography } from '@mui/material';
import { themeToColor } from '../../../theme';
import { paletteColors } from '../chartTypes';

export interface ColorPickerProps {
  label: string;
  color: string;
  onChange: (color: string) => void;
}

function ColorPicker({ label, color, onChange }: ColorPickerProps) {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        justifyContent: 'space-between',
      }}
    >
      <Select
        size="small"
        value={color}
        onChange={(e) => onChange(e.target.value as string)}
        sx={{
          minWidth: 100,
          height: 32,
          '& .MuiSelect-select': {
            display: 'flex',
            alignItems: 'center',
            px: 1,
          },
        }}
        renderValue={(selected) => (
          <Box
            sx={{
              width: '100%',
              height: 14,
              borderRadius: 1,
              bgcolor: selected,
            }}
          />
        )}
      >
        {paletteColors.map((palletColor) => (
          <MenuItem key={palletColor} value={themeToColor(palletColor)} sx={{ py: 1, px: 1 }}>
            <Box
              sx={{
                width: '100%',
                height: 14,
                borderRadius: 1,
                bgcolor: palletColor,
              }}
            />
          </MenuItem>
        ))}
      </Select>
      <Typography
        sx={{
          flex: 1,
          textAlign: 'left',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        }}
      >
        {label}
      </Typography>
    </Box>
  );
}

export default ColorPicker;
