import { useState, useEffect, useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Layout } from 'react-grid-layout';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Autocomplete, TextField, Box } from '@mui/material';
import BarChartIcon from '@mui/icons-material/BarChart';
import PieChartIcon from '@mui/icons-material/PieChart';
import TimelineIcon from '@mui/icons-material/Timeline';
import TableViewIcon from '@mui/icons-material/TableView';
import { LoadingButton } from '@mui/lab';
import {
  ChartType,
  ChartTypeDisplayMap,
  ChartOrientation,
  ChartOrientationDisplayMap,
  RollingPeriod,
  RollingPeriodDisplay,
  ChartConfigurationFormInput,
  isStackedByChart,
} from './chartTypes';
import {
  StatsModule,
  StatsModuleDisplayMap,
  StatsGroupBy,
  StatsGroupByDisplayMap,
  StatsTimeSeriesGroupByModuleMap,
  STATS_MODULE_PERMISSION_MAP,
} from '../stats/statsTypes';
import { useGetStatsQuery } from '../stats/statsApi';
import getDateRangeFromRollingPeriod, { getColors, getFieldName, useTransformedChartData } from './chartFunctions';
import useGuard from '../guard/guardHooks';
import { UserRole } from '../user/userTypes';
import { chartSettingsSchema } from '../dashboard/dashboardSchema';
import { preventSubmitOnEnter } from '../../utils';
import ColorPickerAccordion, { ColorItem } from './components/ColorAccordion';

interface ChartConfigurationDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (chartSetting: ChartConfigurationFormInput, index?: number) => void;
  groupId: number;
  chartIndex?: number;
  chartSetting?: ChartConfigurationFormInput;
}

function normalizeGroupBy(value: StatsGroupBy | StatsGroupBy[] | null | undefined): StatsGroupBy[] {
  if (value == null) return [];
  return Array.isArray(value) ? value : [value];
}

const ChartTypeIconMap: Record<ChartType, React.ReactElement> = {
  [ChartType.PIE]: <PieChartIcon />,
  [ChartType.BAR]: <BarChartIcon />,
  [ChartType.LINE]: <TimelineIcon />,
  [ChartType.PIVOT_TABLE]: <TableViewIcon />,
};

const defaultSchema: ChartConfigurationFormInput = {
  id: 'new-chart',
  name: '',
  module: null,
  type: null,
  groupBy: null,
  stackedBy: null,
  rollingPeriod: null,
  orientation: null,
  layouts: {},
  fieldColorMapping: {},
};

export default function ChartConfigurationDialogControlled({
  open,
  onClose,
  onSave,
  groupId,
  chartIndex,
  chartSetting,
}: ChartConfigurationDialogProps) {
  const [defaultValues, setDefaultValues] = useState<Partial<ChartConfigurationFormInput>>({});
  const [colorAccordionOpen, setColorAccordionOpen] = useState(false);

  const guard = useGuard(groupId);

  const { control, watch, handleSubmit, reset, setValue } = useForm<ChartConfigurationFormInput>({
    resolver: yupResolver(chartSettingsSchema),
    defaultValues: { ...defaultSchema, ...defaultValues },
  });

  const module = watch('module');
  const chartType = watch('type');
  const groupBy = watch('groupBy');
  const stackedBy = watch('stackedBy');
  const rollingPeriod = watch('rollingPeriod');

  const { startDate, endDate } = getDateRangeFromRollingPeriod(rollingPeriod || RollingPeriod.LAST_YEAR);

  const skip =
    !module ||
    !startDate ||
    !endDate ||
    !chartType ||
    !rollingPeriod ||
    (chartType !== ChartType.LINE && !groupBy) ||
    (chartType === ChartType.PIVOT_TABLE && !stackedBy);

  const { data: statsData, isFetching } = useGetStatsQuery(
    {
      ancestorGroupId: groupId,
      module: module ?? undefined,
      groupBy:
        groupBy && stackedBy
          ? [...normalizeGroupBy(groupBy), ...normalizeGroupBy(stackedBy)]
          : normalizeGroupBy(groupBy),
      startDate,
      endDate,
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    },
    {
      skip,
    }
  );

  const chartData = useTransformedChartData(statsData, groupBy, stackedBy, chartSetting?.fieldColorMapping ?? {});

  const colorByDisplay = useMemo(() => getColors(chartData, !!stackedBy), [chartData, stackedBy]);

  useEffect(() => {
    reset({ ...defaultSchema, ...defaultValues });
  }, [defaultValues, reset]);

  useEffect(() => {
    if (chartSetting) {
      setDefaultValues(chartSetting);
    }
  }, [chartSetting]);

  useEffect(() => {
    if (isFetching) return;
    // ? Needed for sorting the field colors
    setValue('fieldColorMapping', Object.fromEntries(colorByDisplay));
  }, [chartData, isFetching, colorByDisplay, setValue]);

  useEffect(() => {
    if (module && groupBy && rollingPeriod) {
      setColorAccordionOpen(true);
    }
  }, [module, groupBy, stackedBy, rollingPeriod]);

  // #region Event handlers & helpers

  const handleSaveChart = (formData: ChartConfigurationFormInput) => {
    const id = formData.id === 'new-chart' ? Math.random().toString(36).substring(2, 15) : formData.id;

    let layout: Record<string, Layout>;
    if (formData.id === 'new-chart') {
      layout = {
        lg: { i: id, x: 0, y: Infinity, w: 6, h: 4 },
      };
    } else if (formData.layouts) {
      layout = formData.layouts;
    } else {
      layout = {};
    }

    const updatedChartSetting = {
      ...formData,
      id,
      layouts: layout,
    };

    onSave(updatedChartSetting, chartIndex);
  };

  const availableModules = Object.values(StatsModule).filter((availableModule) => {
    const requiredPermission = STATS_MODULE_PERMISSION_MAP[availableModule];
    return guard.hasRole(UserRole.TENANT_ADMIN) || guard.hasPermission(requiredPermission);
  });

  const getAvailableGroupBys = (): StatsGroupBy[] => {
    if (!module) return [];
    return Object.values(StatsGroupBy).filter((gb) => gb.startsWith(module));
  };

  // #endregion

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Configure chart</DialogTitle>
      <DialogContent dividers>
        <form
          id="chart-setting-mini-wizard"
          role="presentation"
          onSubmit={handleSubmit(handleSaveChart)}
          onKeyDown={preventSubmitOnEnter}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Controller
              name="name"
              control={control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  label="Chart name"
                  size="small"
                  fullWidth
                  autoComplete="off"
                  margin="normal"
                  error={!!error}
                  helperText={error?.message}
                />
              )}
            />

            <Controller
              name="module"
              control={control}
              render={({ field: { onChange, value, ...rest }, fieldState: { error } }) => (
                <Autocomplete
                  options={availableModules}
                  getOptionLabel={(option) => (option ? StatsModuleDisplayMap[option] : '')}
                  value={value}
                  onChange={(_, newValue) => {
                    if (chartType === ChartType.LINE && newValue) {
                      setValue('groupBy', StatsTimeSeriesGroupByModuleMap[newValue]);
                    } else {
                      setValue('groupBy', null);
                      setValue('stackedBy', null);
                    }
                    return onChange(newValue);
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      {...rest}
                      label="Module"
                      size="small"
                      margin="normal"
                      fullWidth
                      error={!!error}
                      helperText={error?.message}
                    />
                  )}
                />
              )}
            />

            <Controller
              name="type"
              control={control}
              render={({ field: { onChange, value, ...rest }, fieldState: { error } }) => (
                <Autocomplete
                  options={Object.values(ChartType)}
                  getOptionLabel={(option) => (option ? ChartTypeDisplayMap[option] : '')}
                  value={value}
                  onChange={(_, newValue) => {
                    if (!newValue) {
                      return onChange(newValue);
                    }

                    if (newValue === ChartType.LINE && module) {
                      setValue('groupBy', StatsTimeSeriesGroupByModuleMap[module]);
                    }

                    if (newValue !== ChartType.BAR) {
                      setValue('orientation', null);
                    }

                    if (!isStackedByChart(newValue)) {
                      setValue('stackedBy', null);
                    }

                    return onChange(newValue);
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      {...rest}
                      label="Chart type"
                      size="small"
                      fullWidth
                      margin="normal"
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: value ? (
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              ml: 1,
                              mr: -0.5,
                            }}
                          >
                            {ChartTypeIconMap[value]}
                          </Box>
                        ) : null,
                      }}
                    />
                  )}
                  renderOption={(props, option) => (
                    <Box
                      component="li"
                      {...props}
                      sx={{
                        display: 'flex !important',
                        alignItems: 'center',
                        gap: 1,
                        p: '8px 16px',
                        '& > svg': {
                          fontSize: 20,
                          color: 'text.secondary',
                        },
                      }}
                    >
                      {ChartTypeIconMap[option]}
                      <span>{ChartTypeDisplayMap[option]}</span>
                    </Box>
                  )}
                />
              )}
            />

            {chartType !== ChartType.LINE && (
              <Controller
                name="groupBy"
                control={control}
                render={({ field: { onChange, value, ...rest }, fieldState: { error } }) => (
                  <Autocomplete
                    options={getAvailableGroupBys()}
                    getOptionLabel={(option) => (option ? StatsGroupByDisplayMap[option as StatsGroupBy] : '')}
                    value={value}
                    onChange={(_, newValue) => onChange(newValue)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        {...rest}
                        label={chartType === ChartType.PIVOT_TABLE ? 'Rows' : 'Group by'}
                        size="small"
                        margin="normal"
                        fullWidth
                        error={!!error}
                        helperText={error?.message}
                      />
                    )}
                  />
                )}
              />
            )}

            {(((chartType === ChartType.BAR || chartType === ChartType.LINE) && groupBy) ||
              chartType === ChartType.PIVOT_TABLE) && (
              <Controller
                name="stackedBy"
                control={control}
                render={({ field: { onChange, value, ...rest }, fieldState: { error } }) => (
                  <Autocomplete
                    options={getAvailableGroupBys().filter((option) => option !== groupBy)}
                    getOptionLabel={(option) => (option ? StatsGroupByDisplayMap[option as StatsGroupBy] : '')}
                    value={value}
                    onChange={(_, newValue) => onChange(newValue)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        {...rest}
                        label={chartType === ChartType.PIVOT_TABLE ? 'Columns' : 'Stacked by (optional)'}
                        size="small"
                        margin="normal"
                        fullWidth
                        error={!!error}
                        helperText={error?.message}
                      />
                    )}
                  />
                )}
              />
            )}

            <Controller
              name="rollingPeriod"
              control={control}
              render={({ field: { onChange, value, ...rest }, fieldState: { error } }) => (
                <Autocomplete
                  options={Object.values(RollingPeriod)}
                  getOptionLabel={(option) => (option ? RollingPeriodDisplay[option as RollingPeriod] : '')}
                  value={value}
                  onChange={(_, newValue) => onChange(newValue)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      {...rest}
                      label="Time period"
                      size="small"
                      margin="normal"
                      fullWidth
                      error={!!error}
                      helperText={error?.message}
                    />
                  )}
                />
              )}
            />

            {chartType === ChartType.BAR && (
              <Controller
                name="orientation"
                control={control}
                render={({ field: { onChange, value, ...rest }, fieldState: { error } }) => (
                  <Autocomplete
                    options={Object.values(ChartOrientation)}
                    getOptionLabel={(option) => (option ? ChartOrientationDisplayMap[option as ChartOrientation] : '')}
                    value={value}
                    onChange={(_, newValue) => onChange(newValue)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        {...rest}
                        label="Chart orientation"
                        size="small"
                        margin="normal"
                        fullWidth
                        error={!!error}
                        helperText={error?.message}
                      />
                    )}
                  />
                )}
              />
            )}

            {!skip &&
              chartType !== ChartType.PIVOT_TABLE &&
              !(chartType === ChartType.LINE && !stackedBy) &&
              !(chartType === ChartType.BAR && !stackedBy) && (
                <Controller
                  name="fieldColorMapping"
                  control={control}
                  render={({ field: { value = {}, onChange } }) => {
                    const items: ColorItem[] = Object.entries(value).map(([id, color]) => ({
                      id,
                      color,
                      label: getFieldName(id, stackedBy ?? groupBy ?? undefined),
                    }));

                    const handleItemsChange = (newItems: ColorItem[]) => {
                      const newMap: Record<string, string> = Object.fromEntries(
                        newItems.map(({ id, color }) => [id, color])
                      );
                      onChange(newMap);
                    };

                    return (
                      <ColorPickerAccordion
                        title={stackedBy ? 'Stacked by colours' : 'Grouped by colours'}
                        items={items}
                        onItemsChange={handleItemsChange}
                        isLoading={isFetching}
                        expanded={colorAccordionOpen}
                        setExpanded={setColorAccordionOpen}
                      />
                    );
                  }}
                />
              )}
          </Box>
        </form>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <LoadingButton type="submit" form="chart-setting-mini-wizard" variant="contained" color="primary">
          Save
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
}
