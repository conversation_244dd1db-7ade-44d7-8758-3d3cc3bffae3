import React, { useState, useRef, useLayoutEffect, useMemo } from 'react';
import { Box, Typography, IconButton, Paper, CircularProgress, Menu, MenuItem } from '@mui/material';
import SettingsIcon from '@mui/icons-material/SettingsOutlined';
import DeleteIcon from '@mui/icons-material/DeleteOutlined';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import dayjs from 'dayjs';
import { useGetStatsQuery } from '../stats/statsApi';
import { StatsGroupBy } from '../stats/statsTypes';
import getDateRangeFromRollingPeriod, { useTransformedChartData } from './chartFunctions';
import { ChartSetting, ChartType } from './chartTypes';
import BarChart from './components/BarChart';
import LineChart from './components/LineChart';
import Pie<PERSON><PERSON> from './components/PieChart';
import PivotTable from './components/PivotTable';

interface ChartProps {
  groupId: number;
  chartSetting: ChartSetting;
  index: number;
  editMode: boolean;
  onEditChart: (index: number) => void;
  onDeleteChart: (index: number) => void;
}

function Chart({ groupId, chartSetting, index, editMode, onEditChart, onDeleteChart }: ChartProps) {
  const {
    groupBy,
    fieldColorMapping,
    type: chartType,
    module: moduleType,
    rollingPeriod,
    stackedBy,
    name,
    id,
    orientation,
  } = chartSetting;
  const { startDate, endDate } = getDateRangeFromRollingPeriod(rollingPeriod);
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [headerHeight, setHeaderHeight] = useState(0);

  const headerRef = useRef<HTMLDivElement>(null);
  const open = Boolean(anchorEl);
  const groupByParams = useMemo(
    () => [groupBy, stackedBy].filter<StatsGroupBy>((x): x is StatsGroupBy => Boolean(x)),
    [groupBy, stackedBy]
  );

  const { data: statsData, isLoading } = useGetStatsQuery({
    ancestorGroupId: groupId,
    startDate,
    endDate,
    module: moduleType,
    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    groupBy: groupByParams,
  });

  const chartData = useTransformedChartData(statsData, groupBy, stackedBy, fieldColorMapping ?? {});

  useLayoutEffect(() => {
    if (headerRef.current) {
      setHeaderHeight(headerRef.current.offsetHeight);
    }
  }, [name, editMode]);

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(e.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const renderChartHeader = () => (
    <Box
      sx={{
        pl: 2.25,
        pr: 0.5,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        height: '40px',
      }}
    >
      <Typography sx={{ lineHeight: 1.25 }}>{name}</Typography>
      {editMode && (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton size="small" className="drag-handle" sx={{ cursor: 'move' }}>
            <DragIndicatorIcon fontSize="small" />
          </IconButton>
          <IconButton
            id={`more-button-${id}`}
            aria-controls={open ? `more-menu-${id}` : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            onClick={handleClick}
          >
            <MoreVertIcon fontSize="small" />
          </IconButton>
          <Menu
            id={`more-menu-${id}`}
            anchorEl={anchorEl}
            open={open}
            onClose={handleClose}
            MenuListProps={{
              'aria-labelledby': `more-button-${id}`,
            }}
          >
            <MenuItem
              onClick={() => {
                onEditChart(index);
                handleClose();
              }}
            >
              <SettingsIcon fontSize="small" sx={{ mr: 1 }} />
              <Typography>Edit</Typography>
            </MenuItem>
            <MenuItem
              onClick={() => {
                onDeleteChart(index);
                handleClose();
              }}
            >
              <DeleteIcon color="error" fontSize="small" sx={{ mr: 1 }} />
              <Typography color="error">Delete</Typography>
            </MenuItem>
          </Menu>
        </Box>
      )}
    </Box>
  );

  const renderChartBody = () => {
    if (isLoading) {
      return (
        <Box
          sx={{
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <CircularProgress size={24} />
        </Box>
      );
    }

    if (chartData.size === 0) {
      return (
        <Box
          sx={{
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography color="text.secondary">No data available</Typography>
        </Box>
      );
    }

    switch (chartType) {
      case ChartType.PIE:
        return <PieChart data={chartData} />;

      case ChartType.BAR:
        return <BarChart data={chartData} module={moduleType} orientation={orientation} stackedBy={!!stackedBy} />;

      case ChartType.LINE:
        return (
          <LineChart
            data={chartData}
            startDate={startDate ?? dayjs().subtract(6, 'months').valueOf()}
            endDate={endDate ?? dayjs().valueOf()}
            stackedBy={!!stackedBy}
          />
        );

      case ChartType.PIVOT_TABLE:
        if (!stackedBy) {
          return <Box sx={{ p: 2, textAlign: 'center' }}>Pivot table requires both row and column grouping</Box>;
        }
        return <PivotTable data={chartData} />;

      default:
        return <Box sx={{ p: 2, textAlign: 'center' }}>Unsupported chart type</Box>;
    }
  };

  return (
    <Paper sx={{ display: 'flex', flexDirection: 'column', height: '100%', width: '100%' }} elevation={4}>
      <Box ref={headerRef}>{renderChartHeader()}</Box>
      <Box
        sx={{
          width: '100%',
          height: headerHeight ? `calc(100% - ${headerHeight}px)` : '100%',
          flex: 1,
        }}
      >
        {renderChartBody()}
      </Box>
    </Paper>
  );
}

export default Chart;
