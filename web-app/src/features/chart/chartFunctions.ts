import dayjs from 'dayjs';
import { useMemo } from 'react';
import {
  ChartConfigurationFormInput,
  ChartField,
  ChartFields,
  ChartSetting,
  isChartField,
  isChartFieldWithStacks,
  paletteColors,
  RollingPeriod,
} from './chartTypes';
import { themeToColor } from '../../theme';
import {
  hasMetaMap,
  KeyTotal,
  StatsGroupBy,
  StatsGroupByFieldMetaMap,
  StatsKeyValue,
  TimeSeriesDataPoint,
} from '../stats/statsTypes';
import { isCountObject, sortByGroupKeys } from '../stats/statsFunctions';

export default function getDateRangeFromRollingPeriod(period: RollingPeriod): { startDate: number; endDate: number } {
  const end = dayjs().endOf('day');
  let start: dayjs.Dayjs;

  switch (period) {
    case RollingPeriod.LAST_7_DAYS:
      start = end.subtract(7, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_30_DAYS:
      start = end.subtract(30, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_90_DAYS:
      start = end.subtract(90, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_6_MONTHS:
      start = end.subtract(6, 'months').startOf('day');
      break;
    case RollingPeriod.LAST_YEAR:
      start = end.subtract(1, 'year').startOf('day');
      break;
    default:
      start = end.subtract(7, 'days').startOf('day');
  }

  return {
    startDate: start.valueOf(),
    endDate: end.valueOf(),
  };
}

export function randomThemeColor(): string {
  return themeToColor(paletteColors[Math.floor(Math.random() * paletteColors.length)]);
}

/**
 * Get a color for a key based on various color mapping strategies
 *
 * @param key The key to get a color for
 * @param fieldColorMapping Optional user-defined mapping of fields to colors
 * @param groupBy Optional grouping parameter to look up predefined colors from metadata
 * @returns The color for the key - either from fieldColorMapping, metadata, or a random theme color
 */
export function getFieldColor(key: string, fieldColorMapping: Record<string, string>, groupBy?: StatsGroupBy): string {
  const color = fieldColorMapping[key];
  if (color) {
    return color;
  }

  if (!groupBy || !hasMetaMap(groupBy)) {
    return randomThemeColor();
  }

  const metaValue = StatsGroupByFieldMetaMap[groupBy][key];
  if (!metaValue) {
    return randomThemeColor();
  }

  return metaValue.color;
}
/**
 * Get a display name for a key based on metadata mapping
 *
 * @param key The key to get a display name for
 * @param groupBy Optional grouping parameter to look up predefined names from metadata
 * @returns The display name for the key - either from metadata or the original key
 */
export function getFieldName(key: string, groupBy?: StatsGroupBy): string {
  if (!groupBy || !hasMetaMap(groupBy)) {
    return key;
  }

  const metaValue = StatsGroupByFieldMetaMap[groupBy][key];
  if (!metaValue) {
    return key;
  }

  return metaValue.label;
}

// helper to elide with an ellipsis:
export const truncate = (s: string, max: number) => (max > 0 && s.length > max ? `${s.slice(0, max)}…` : s);

// ? Helper to extract the value for a given key
export const extractY = (total: number | KeyTotal[], key: string) => {
  const items = Array.isArray(total) ? total : [{ key, total }];
  return items.find((item) => item.key === key)?.total ?? 0;
};

export const convertChartSettingToForm = (chartSetting: ChartSetting): ChartConfigurationFormInput => ({
  ...chartSetting,
});

export const convertFormToChartSetting = (form: ChartConfigurationFormInput): ChartSetting => {
  const { fieldArrayId, ...rest } = form;
  const entries = Object.entries(rest).filter(([, v]) => v != null);
  return Object.fromEntries(entries) as unknown as ChartSetting;
};

/**
 * Transforms nested statistics data into a flat map structure for stacked chart visualization
 * Steps:
 * 1. Sort the input data by keys for consistent ordering
 * 2. Create a new Map to store transformed data
 * 3. For each data point:
 *    - Get human-readable label using metadata
 *    - Assign color from mapping or metadata
 *    - Extract count value if available
 * @param statsData - Raw statistics data to transform
 * @param fieldColorMapping - User-defined color mappings
 * @param stackedBy - Grouping parameter for stacked visualization
 * @returns Map of transformed chart fields with labels, colors and counts
 */
function transformStackedData(
  statsData: StatsKeyValue,
  fieldColorMapping: Record<string, string>,
  stackedBy: StatsGroupBy
): Map<string, ChartField> {
  const sortedData = sortByGroupKeys(statsData, stackedBy);
  const newDataOut: Map<string, ChartField> = new Map();

  Object.entries(sortedData).forEach(([key, value]) => {
    const label = getFieldName(key, stackedBy);
    const color = getFieldColor(key, fieldColorMapping, stackedBy);
    newDataOut.set(key, { label, color, count: isCountObject(value) ? value.count : 0 });
  });

  return newDataOut;
}

/**
 * Main transformation function that converts raw statistics data into chart-ready format
 * Steps:
 * 1. Validate input data and grouping parameters
 * 2. Sort data by primary grouping keys
 * 3. Transform each data point:
 *    - For simple counts: create direct chart fields
 *    - For stacked data: create nested structure using transformStackedData
 * 4. Apply display names and colors based on metadata and user preferences
 * @param statsData - Raw statistics data
 * @param groupBy - Primary grouping parameter
 * @param stackedBy - Secondary grouping for stacked visualizations
 * @param fieldColorMapping - User-defined color mappings
 * @returns Transformed chart fields with optional stacking
 */
export function transformChartData(
  statsData: StatsKeyValue | undefined,
  groupBy: StatsGroupBy | undefined | null,
  stackedBy: StatsGroupBy | undefined | null,
  fieldColorMapping: Record<string, string>
): ChartFields {
  if (!statsData || !groupBy) {
    return new Map();
  }

  const sortedData = sortByGroupKeys(statsData, groupBy);
  const newDataOut: ChartFields = new Map();

  Object.entries(sortedData).forEach(([key, value]) => {
    const displayKey = getFieldName(key, groupBy);
    const color = getFieldColor(key, fieldColorMapping, groupBy);

    if (isCountObject(value)) {
      newDataOut.set(key, { label: displayKey, color, count: value.count });
    } else if (stackedBy) {
      const stackedData = transformStackedData(value, fieldColorMapping, stackedBy);
      newDataOut.set(key, { label: displayKey, color, stacks: stackedData });
    }
  });

  return newDataOut;
}

/**
 * React hook that memoizes transformed chart data
 * Steps:
 * 1. Memoize color mapping to prevent unnecessary updates
 * 2. Transform data only when inputs change
 * @param statsData - Raw statistics data
 * @param groupBy - Primary grouping parameter
 * @param stackedBy - Secondary grouping for stacked visualizations
 * @param fieldColorMapping - User-defined color mappings
 * @returns Memoized transformed chart data
 */
export function useTransformedChartData(
  statsData: StatsKeyValue | undefined,
  groupBy: StatsGroupBy | undefined | null,
  stackedBy: StatsGroupBy | undefined | null,
  fieldColorMapping: Record<string, string>
) {
  return useMemo(
    () => transformChartData(statsData, groupBy, stackedBy, fieldColorMapping),
    [statsData, groupBy, stackedBy, fieldColorMapping]
  );
}
/**
 * Gets a map of labels for chart fields, handling both stacked and unstacked data.
 * The order of the labels in the returned map isn't guaranteed.
 * @param data - The chart fields data
 * @param stackedBy - Whether the data is stacked
 * @returns Map of field keys to their labels
 */
export function getLabels(data: ChartFields, stackedBy: boolean): Map<string, string> {
  const labels = new Map<string, string>();

  if (stackedBy) {
    data.entries().forEach(([, value]) => {
      if (isChartFieldWithStacks(value)) {
        value.stacks?.entries().forEach(([stackKey, stackValue]) => {
          if (!labels.has(stackKey)) {
            labels.set(stackKey, stackValue.label ?? stackKey);
          }
        });
      }
    });
  } else {
    data.entries().forEach(([key, value]) => {
      labels.set(key, value.label ?? key);
    });
  }

  return labels;
}

/**
 * Gets a mapping of colors for chart fields, handling both stacked and unstacked data
 * @param data - The chart fields data
 * @param stackedBy - Whether the data is stacked
 * @returns Map of field keys to their colors
 */
export function getColors(data: ChartFields, stackedBy: boolean): Map<string, string> {
  const colors: Map<string, string> = new Map();

  if (stackedBy) {
    data.entries().forEach(([, value]) => {
      if (isChartFieldWithStacks(value)) {
        value.stacks?.entries().forEach(([stackKey, stackValue]) => {
          if (!colors.has(stackKey)) {
            colors.set(stackKey, stackValue.color ?? themeToColor('primary.main'));
          }
        });
      }
    });
  } else {
    data.entries().forEach(([key, value]) => {
      colors.set(key, value.color ?? themeToColor('primary.main'));
    });
  }

  return colors;
}

export function chartFieldsToTimeSeries(data: ChartFields): TimeSeriesDataPoint[] {
  return data
    .entries()
    .map(([date, value]) => {
      if (isChartField(value)) {
        return { date, total: value.count };
      }
      return {
        date,
        total: value.stacks?.entries().reduce((acc, [, stackValue]) => acc + (stackValue.count || 0), 0) ?? 0,
      };
    })
    .toArray();
}
