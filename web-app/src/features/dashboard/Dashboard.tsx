import { useState, useEffect } from 'react';
import { Layout, Layouts, Responsive, WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import {
  Box,
  Typography,
  Button,
  CircularProgress,
  IconButton,
  Menu,
  MenuItem,
  styled,
  Tabs,
  Tab,
} from '@mui/material';
import EditIcon from '@mui/icons-material/EditOutlined';
import SaveIcon from '@mui/icons-material/Save';
import InsertChartOutlinedIcon from '@mui/icons-material/InsertChartOutlined';
import AddIcon from '@mui/icons-material/Add';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import DeleteIcon from '@mui/icons-material/DeleteOutlined';
import { useNavigate, useParams } from 'react-router-dom';
import { useFieldArray, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton, TabContext, TabPanel } from '@mui/lab';
import PeopleAltOutlinedIcon from '@mui/icons-material/PeopleAltOutlined';
import PersonIcon from '@mui/icons-material/Person';
import SettingsIcon from '@mui/icons-material/SettingsOutlined';
import DashboardSettingsDialog from './DashboardSettingsDialog';
import { ChartConfigurationFormInput, ChartSetting } from '../chart/chartTypes';
import ChartRenderer from '../chart/Chart';
import { useGetDashboardSettingQuery, useUpdateDashboardSettingMutation } from './dashboardApi';
import { DashboardFormInput, DashboardSettingUpdate } from './dashBoardTypes';
import { preventSubmitOnEnter } from '../../utils';
import { dashboardSchema } from './dashboardSchema';
import ErrorGate from '../../components/ErrorGate';
import PageTitle from '../title/Title';
import useTabs from '../../hooks/useTabs';
import UnsavedChangesDialog from '../../components/UnsavedChangesDialog';
import DeleteDashboardDialog from './DeleteDashboardDialog';
import { convertChartSettingToForm, convertFormToChartSetting } from '../chart/chartFunctions';
import ChartConfigurationDialogControlled from '../chart/ChartConfigurationDialogForm';

const ResponsiveGridLayout = WidthProvider(Responsive);

// Define breakpoints
const BREAKPOINTS = {
  lg: 1200,
  md: 996,
  sm: 768,
  xs: 480,
  xxs: 0,
};

// Define columns for each breakpoint
const COLS = {
  lg: 12,
  md: 10,
  sm: 6,
  xs: 4,
  xxs: 2,
};

// Use the shared dashboard schema

const defaultSchema: DashboardFormInput = {
  group: null,
  name: '',
  description: '',
  settings: [],
};

function getLayoutPerIdFromLayouts(layouts: Layouts): Record<string, Record<string, Layout>> {
  return Object.keys(layouts).reduce((acc, breakpoint) => {
    const layout = layouts[breakpoint] || [];
    layout.forEach((item) => {
      if (!item.i) return; // Skip items without an ID

      // Initialize the record for this chart ID if it doesn't exist
      if (!acc[item.i]) {
        acc[item.i] = {};
      }

      // Add the layout for this breakpoint
      acc[item.i][breakpoint] = item;
    });
    return acc;
  }, {} as Record<string, Record<string, Layout>>);
}

const StyledStickyBox = styled(Box)(({ theme }) => ({
  [theme.breakpoints.up('md')]: {
    position: 'sticky',
    top: '56px',
    zIndex: 10,
    paddingTop: '8px',
    backgroundColor: 'white',
    paddingLeft: theme.spacing(2),
    paddingRight: theme.spacing(2),
    marginLeft: theme.spacing(-2),
    marginRight: theme.spacing(-2),
  },
})) as typeof Box;

function DashBoard() {
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const menuOpen = Boolean(menuAnchorEl);

  const [defaultValues, setDefaultValues] = useState<Partial<DashboardFormInput>>({});
  const [editMode, setEditMode] = useState(false);

  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [chartToEdit, setChartToEdit] = useState<number | undefined>();

  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const { tabValue, setTabValue } = useTabs('dashboard');
  const { groupId, dashboardId } = useParams();
  const {
    data: dashboard,
    isLoading,
    error,
  } = useGetDashboardSettingQuery(Number(dashboardId), { skip: !dashboardId });
  const [updateDashboard] = useUpdateDashboardSettingMutation();
  const navigate = useNavigate();
  const {
    handleSubmit,
    control,
    reset,
    formState: { isDirty },
  } = useForm<DashboardFormInput>({
    resolver: yupResolver(dashboardSchema),
    defaultValues: { ...defaultSchema, ...defaultValues },
  });

  const {
    fields: charts,
    append,
    remove,
    update,
  } = useFieldArray({
    control,
    name: 'settings',
    keyName: 'fieldArrayId',
  });

  useEffect(() => {
    reset({ ...defaultSchema, ...defaultValues });
  }, [defaultValues, reset]);

  useEffect(() => {
    if (dashboard && dashboard.settings) {
      setDefaultValues({
        name: dashboard.name,
        description: dashboard.description,
        group: dashboard.group,
        settings: dashboard.settings.map(convertChartSettingToForm),
      });
    }
  }, [dashboard]);

  // #region Event handlers & helpers
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleLayoutChange = (_currentLayout: Layout[], allLayouts: Layouts) => {
    if (!editMode) return; // Only process layout changes in edit mode

    // Create a map of layouts by chart ID
    const layoutsPerId = getLayoutPerIdFromLayouts(allLayouts);

    // Update each chart with its new layout
    charts.forEach((chart, index) => {
      if (layoutsPerId[chart.id]) {
        update(index, {
          ...chart,
          layouts: layoutsPerId[chart.id],
        });
      }
    });
  };

  const handleDeleteChart = (index: number) => {
    remove(index);
  };

  const onSubmit = (form: DashboardFormInput): void => {
    setDefaultValues(form);

    const cleanedSettings: ChartSetting[] = form.settings.map((s) => convertFormToChartSetting(s));

    const dashBoardUpdate: DashboardSettingUpdate = {
      id: Number(dashboardId),
      name: form.name,
      description: form.description,
      settings: cleanedSettings,
    };
    updateDashboard(dashBoardUpdate);
    setEditMode(false);
  };

  const handleEditToggle = () => {
    setEditMode(!editMode);
  };

  const handleSaveChart = (chartSetting: ChartConfigurationFormInput, index?: number) => {
    const chart = convertFormToChartSetting(chartSetting);
    if (index !== undefined && index !== null) {
      update(index, chart);
    } else {
      append(chart);
    }
    setConfigDialogOpen(false);
    setChartToEdit(undefined);
  };

  const handleOpenSettingsDialog = () => {
    setSettingsDialogOpen(true);
  };

  const handleOpenDeleteDialog = () => {
    setDeleteDialogOpen(true);
  };

  const handleSettingsSubmit = (data: { name: string; description: string }) => {
    const dashBoardUpdate: DashboardSettingUpdate = {
      id: Number(dashboardId),
      name: data.name,
      description: data.description,
      settings: dashboard?.settings || [],
    };
    updateDashboard(dashBoardUpdate);
    setSettingsDialogOpen(false);
  };

  // #endregion

  // #region Renderers

  const renderCharts = () =>
    charts.map((chart, index) => (
      <div key={chart.id}>
        <ChartRenderer
          groupId={Number(groupId)}
          index={index}
          chartSetting={chart as ChartSetting}
          editMode={editMode}
          onEditChart={(chartToUpdate) => {
            setChartToEdit(chartToUpdate);
            setConfigDialogOpen(true);
          }}
          onDeleteChart={handleDeleteChart}
        />
      </div>
    ));

  const renderTopBar = () => (
    <StyledStickyBox>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" sx={{ mb: 1, width: { xs: '100%', md: 'unset' } }}>
          #{dashboard?.sid} - {dashboard?.name}
        </Typography>

        <Box display="flex" gap={2} flexWrap="wrap" mb={{ xs: 1, md: 0 }}>
          {editMode ? (
            <>
              <Button
                variant="text"
                color="primary"
                onClick={() => {
                  reset(defaultValues);
                  setEditMode(false);
                }}
              >
                Cancel
              </Button>

              <LoadingButton
                type="submit"
                form="dashboard-form"
                loading={isLoading}
                disabled={!isDirty}
                variant="contained"
                color="primary"
                endIcon={<SaveIcon />}
              >
                Save
              </LoadingButton>

              <IconButton
                onClick={() => {
                  setConfigDialogOpen(true);
                }}
              >
                <AddIcon />
              </IconButton>
            </>
          ) : (
            <>
              {charts.length !== 0 && (
                <IconButton onClick={handleEditToggle}>
                  <EditIcon />
                </IconButton>
              )}

              <Box>
                <IconButton
                  color="inherit"
                  id="basic-button"
                  aria-controls={menuOpen ? 'basic-menu' : undefined}
                  aria-haspopup="true"
                  aria-expanded={menuOpen ? 'true' : undefined}
                  onClick={handleMenuOpen}
                >
                  <MoreVertIcon />
                </IconButton>
                <Menu
                  id="basic-menu"
                  anchorEl={menuAnchorEl}
                  open={menuOpen}
                  onClose={handleMenuClose}
                  MenuListProps={{
                    'aria-labelledby': 'basic-button',
                  }}
                >
                  <MenuItem
                    onClick={() => {
                      handleMenuClose();
                      handleOpenSettingsDialog();
                    }}
                  >
                    <SettingsIcon fontSize="small" sx={{ mr: 1 }} />
                    <Typography>Settings</Typography>
                  </MenuItem>
                  <MenuItem
                    onClick={() => {
                      handleMenuClose();
                      handleOpenDeleteDialog();
                    }}
                  >
                    <DeleteIcon color="error" fontSize="small" sx={{ mr: 1 }} />
                    <Typography color="error">Delete</Typography>
                  </MenuItem>
                </Menu>
              </Box>
            </>
          )}
        </Box>
      </Box>
      <Box display="flex" gap={1} flexWrap="wrap">
        <Box display="inline" whiteSpace="nowrap">
          {dashboard && (
            <>
              <PeopleAltOutlinedIcon fontSize="small" sx={{ verticalAlign: 'text-bottom' }} /> {dashboard.group.name}
            </>
          )}
        </Box>
        <Box display="inline" whiteSpace="nowrap">
          {dashboard && (
            <>
              <PersonIcon fontSize="small" sx={{ verticalAlign: 'text-bottom' }} /> {dashboard.createdBy.firstName}{' '}
              {dashboard.createdBy.lastName}
            </>
          )}
        </Box>
      </Box>

      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Tabs value={tabValue} sx={{ borderBottom: '1px solid lightgray', width: '100%' }}>
          <Tab sx={{ borderBottom: 0 }} label="Dashboard" value="dashboard" onClick={() => setTabValue('dashboard')} />
        </Tabs>
      </Box>
    </StyledStickyBox>
  );

  const renderEmptyState = () => (
    <Box sx={{ p: 2 }}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          textAlign: 'center',
          py: 4,
        }}
      >
        <InsertChartOutlinedIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h5" color="text.primary" gutterBottom>
          Add charts to your dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3, maxWidth: 600 }}>
          Create several types of charts - such as pie, bar, or line - to quickly view the status of work.
        </Typography>
        <Button
          variant="contained"
          onClick={() => {
            setConfigDialogOpen(true);
            setEditMode(true);
          }}
          startIcon={<AddIcon />}
        >
          Add Chart
        </Button>
      </Box>
    </Box>
  );

  const renderDashboardContent = () => {
    if (isLoading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
          <CircularProgress />
        </Box>
      );
    }

    if (charts.length === 0 && dashboard?.settings.length === 0) {
      return renderEmptyState();
    }

    return (
      <Box>
        <ResponsiveGridLayout
          className="layout"
          breakpoints={BREAKPOINTS}
          cols={COLS}
          rowHeight={150}
          margin={[16, 16]}
          containerPadding={[0, 0]}
          onLayoutChange={handleLayoutChange}
          draggableHandle=".drag-handle"
          isResizable={editMode}
          isDraggable={editMode}
          useCSSTransforms
          layouts={charts.reduce((acc, chart) => {
            if (chart.layouts) {
              Object.entries(chart.layouts).forEach(([breakpoint, layout]) => {
                if (!acc[breakpoint]) {
                  acc[breakpoint] = [];
                }
                const layoutWithId = { ...layout, i: chart.id };
                acc[breakpoint].push(layoutWithId);
              });
            }
            return acc;
          }, {} as Layouts)}
        >
          {renderCharts()}
        </ResponsiveGridLayout>
      </Box>
    );
  };

  // #endregion

  return (
    <ErrorGate error={error}>
      <PageTitle page={dashboard ? `#${dashboard.sid} - Dashboards` : 'Dashboards'} />
      <TabContext value={tabValue}>
        {renderTopBar()}
        <TabPanel value="dashboard" sx={{ p: 0 }}>
          <form
            id="dashboard-form"
            role="presentation"
            onSubmit={handleSubmit(onSubmit)}
            onKeyDown={preventSubmitOnEnter}
          >
            {renderDashboardContent()}
          </form>
        </TabPanel>

        <UnsavedChangesDialog isDirty={isDirty} />

        {configDialogOpen && (
          <ChartConfigurationDialogControlled
            groupId={Number(groupId)}
            open={configDialogOpen}
            onClose={() => {
              setConfigDialogOpen(false);
              setChartToEdit(undefined);
              if (charts.length === 0 && !isDirty) {
                setEditMode(false);
              }
            }}
            onSave={handleSaveChart}
            chartIndex={chartToEdit}
            chartSetting={chartToEdit !== undefined ? charts[chartToEdit] : undefined}
          />
        )}

        {settingsDialogOpen && (
          <DashboardSettingsDialog
            open={settingsDialogOpen}
            initialValues={{
              name: dashboard?.name || '',
              description: dashboard?.description || '',
            }}
            onClose={() => setSettingsDialogOpen(false)}
            onSubmit={handleSettingsSubmit}
          />
        )}

        {deleteDialogOpen && dashboard && (
          <DeleteDashboardDialog
            open={deleteDialogOpen}
            dashboard={dashboard}
            onClose={() => setDeleteDialogOpen(false)}
            onDelete={() => navigate('./../')}
          />
        )}
      </TabContext>
    </ErrorGate>
  );
}

export default DashBoard;
