import { Dayjs } from 'dayjs';
import { FileDisplay } from '../file/fileTypes';
import { GroupDisplay, GroupListRead, GroupRead } from '../group/groupTypes';
import { User, UserDisplay } from '../user/userTypes';
import { WorkPermitDisplay } from '../work-permit/workPermitTypes';
import { LocationDisplay, LocationFilterMode } from '../location/locationTypes';
import { ConsequenceCategoryRead } from '../incident/consequence/consequenceTypes';
import { CauseRead } from '../incident/cause/causeTypes';
import { EnumMetaItem, EnumMetaMap } from '../../types';
import { themeToColor } from '../../theme';
import { createDynamicMetaMap, getWeekNumberAndYear, sortDates } from '../../utils';

export interface ObservationScoreParams {
  search?: string;
  statusNot?: string;
  status?: ObservationStatus;
  assignedUser?: number;
  score?: SafetyWalkScore;
  createdBy?: number;
  filter?: string;
  ancestorAssignedGroupId?: number;
  assignedGroup?: number;
  candidateGroups?: ObservationCandidateGroups[];
  groupId?: number;
  ancestorId?: number;
  locationId?: number;
  ancestorLocationId?: number;
  pageNumber?: number;
  pageSize?: number;
  sort?: ObservationSort[];
}

export interface ObservationViewState {
  listView: 'mine' | 'all';
  viewMode?: 'table' | 'map';
  score?: SafetyWalkScore;
  group?: GroupListRead;
  assignedGroup?: GroupListRead;
  status?: ObservationStatus;
  createdBy?: User;
  assignedUser?: User;
  candidateGroups?: ObservationCandidateGroups[];
  search?: string;
  sort?: ObservationSort[];
  columns?: ObservationColumnSetting[];
  location?: LocationDisplay;
  locationFilterMode?: LocationFilterMode;
}

export interface ObservationScorePDFParams {
  id: number;
  timeZone: string;
  withFiles: boolean;
}
export interface ObservationCreate {
  description: string;
  location: number;
  files: number[];
}

export interface ObservationRead {
  sid: number;
  description: string;
  location: LocationDisplay;
  files: FileDisplay[];
}

export interface ObservationScoreCreate {
  group: number;
  date: number;
  score: SafetyWalkScore;
  workPermit?: number;
  category?: number;
  cause?: number;
  observation: ObservationCreate;
}

export interface ObservationScoreUpdate {
  id: number;
  date: number;
  score: SafetyWalkScore;
  workPermit?: number;
  category?: number;
  cause?: number;
  observation: ObservationCreate;
}

export interface ObservationScoreRead {
  id: number;
  group: GroupRead;
  date: number;
  score: SafetyWalkScore;
  workPermit?: WorkPermitDisplay;
  category?: ConsequenceCategoryRead;
  cause?: CauseRead;
  observation: ObservationRead;
  createdAt: number;
  modifiedAt: number;
  createdBy: UserDisplay;
  modifiedBy: UserDisplay;
  status: ObservationStatus;
  processInstanceId: string;
  locked: boolean;
  assignedGroup?: GroupDisplay;
  assignedUser?: UserDisplay;
  safetyWalk?: SafetyWalkDisplay;
  question?: QuestionDisplay;
}

export interface SafetyWalkDisplay {
  id: number;
  sid: number;
  status: ObservationStatus;
  locked: boolean;
}

export interface QuestionDisplay {
  id: number;
  name: string;
}

export enum SafetyWalkScore {
  SAFE = 'SAFE',
  OPPORTUNITY_FOR_IMPROVEMENT = 'OPPORTUNITY_FOR_IMPROVEMENT',
  VIOLATION_OF_LAW_OR_REGULATION = 'VIOLATION_OF_LAW_OR_REGULATION',
  NOT_APPLICABLE = 'NOT_APPLICABLE',
}

export const SafetyWalkScoreMeta: EnumMetaMap<SafetyWalkScore> = {
  [SafetyWalkScore.SAFE]: { label: 'Safe', color: themeToColor('primary.main') },
  [SafetyWalkScore.OPPORTUNITY_FOR_IMPROVEMENT]: {
    label: 'Opportunity for improvement',
    color: themeToColor('warning.light'),
  },
  [SafetyWalkScore.VIOLATION_OF_LAW_OR_REGULATION]: { label: 'Non-conformity', color: themeToColor('error.main') },
  [SafetyWalkScore.NOT_APPLICABLE]: { label: 'Not applicable', color: themeToColor('action.disabled') },
};

export const SafetyWalkScoreDisplayMap = Object.fromEntries(
  Object.entries(SafetyWalkScoreMeta).map(([key, meta]) => [key, meta?.label ?? ''])
) as Record<SafetyWalkScore, string>;

export const SafetyWalkScoreColorMap = Object.fromEntries(
  Object.entries(SafetyWalkScoreMeta).map(([key, meta]) => [key, meta?.color ?? ''])
) as Record<SafetyWalkScore, string>;

export enum ObservationStatus {
  REPORTED = 'REPORTED',
  ASSIGNED = 'ASSIGNED',
  RESOLVED = 'RESOLVED',
  CANCELED = 'CANCELED',
}

export const ObservationStatusMeta: EnumMetaMap<ObservationStatus> = {
  [ObservationStatus.REPORTED]: { label: 'Reported', color: themeToColor('primary.main') },
  [ObservationStatus.ASSIGNED]: { label: 'Assigned', color: themeToColor('secondary.main') },
  [ObservationStatus.RESOLVED]: { label: 'Resolved', color: themeToColor('success.main') },
  [ObservationStatus.CANCELED]: { label: 'Canceled', color: themeToColor('error.main') },
};

export const ObservationStatusDisplayMap = Object.fromEntries(
  Object.entries(ObservationStatusMeta).map(([key, meta]) => [key, meta?.label ?? ''])
) as Record<ObservationStatus, string>;

export interface ObservationFormInput {
  group: GroupDisplay | null;
  description: string;
  location: LocationDisplay | null;
  workPermit: WorkPermitDisplay | null;
  category: ConsequenceCategoryRead | null;
  cause: CauseRead | null;
  score: SafetyWalkScore | null;
  date: Dayjs | null;
  files: FileDisplay[];
}

export interface ObservationChange {
  by: UserDisplay;
  at: number;
  type: ObservationChangeType;
  oldEntity: ObservationScoreRead;
  newEntity: ObservationScoreRead;
}

export enum ObservationChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export const ObservationChangeTypeDisplayMap: Record<ObservationChangeType, string> = {
  INSERT: 'Observation reported',
  UPDATE: 'Observation updated',
  DELETE: 'Observation deleted',
};

export enum ObservationCandidateGroups {
  OBSERVATION_ASSIGN = 'OBSERVATION_ASSIGN',
  OBSERVATION_RESOLVE = 'OBSERVATION_RESOLVE',
}

export enum ObservationCandidateGroupsAssigned {
  OBSERVATION_RESOLVE = 'OBSERVATION_RESOLVE',
}

export enum ObservationCandidateGroupsReported {
  OBSERVATION_ASSIGN = 'OBSERVATION_ASSIGN',
}

export const ObservationCandidateGroupDisplayMap: Record<ObservationCandidateGroups, string> = {
  OBSERVATION_ASSIGN: 'Assign',
  OBSERVATION_RESOLVE: 'Resolve',
};

export interface ObservationState {
  observationViewState: ObservationViewState;
}

export enum ObservationColumn {
  SID = 'observation.sid',
  DESCRIPTION = 'observation.description',
  SCORE = 'score',
  GROUP = 'group',
  ASSIGNED_GROUP = 'assignedGroup',
  ASSIGNED_USER = 'assignedUser',
  LOCATION = 'observation.location',
  STATUS = 'status',
  DATE = 'date',
}

export const ObservationColumnDisplayMap: Record<ObservationColumn, string> = {
  [ObservationColumn.SID]: 'ID',
  [ObservationColumn.DESCRIPTION]: 'Description',
  [ObservationColumn.SCORE]: 'Score',
  [ObservationColumn.GROUP]: 'Reported group',
  [ObservationColumn.ASSIGNED_GROUP]: 'Assigned group',
  [ObservationColumn.ASSIGNED_USER]: 'Assigned user',
  [ObservationColumn.LOCATION]: 'Location',
  [ObservationColumn.STATUS]: 'Status',
  [ObservationColumn.DATE]: 'Date',
};

export const ObservationColumnDefaults: ObservationColumnSetting[] = [
  {
    column: ObservationColumn.SID,
    hidden: false,
    width: 75,
  },
  {
    column: ObservationColumn.DESCRIPTION,
    hidden: false,
    width: 500,
  },
  {
    column: ObservationColumn.SCORE,
    hidden: false,
    width: 75,
  },
  {
    column: ObservationColumn.GROUP,
    hidden: false,
    width: 200,
  },
  {
    column: ObservationColumn.ASSIGNED_GROUP,
    hidden: false,
    width: 200,
  },
  {
    column: ObservationColumn.ASSIGNED_USER,
    hidden: false,
    width: 200,
  },
  {
    column: ObservationColumn.LOCATION,
    hidden: false,
    width: 200,
  },
  {
    column: ObservationColumn.STATUS,
    hidden: false,
    width: 130,
  },
  {
    column: ObservationColumn.DATE,
    hidden: false,
    width: 175,
  },
];

export interface ObservationColumnSetting {
  column: ObservationColumn;
  hidden?: boolean;
  width?: number;
}

export enum ObservationSortField {
  SID = 'sid',
  DATE = 'date',
}

export const ObservationFieldSortMap: Partial<
  Record<keyof ObservationScoreRead | 'observation.sid', ObservationSortField>
> = {
  'observation.sid': ObservationSortField.SID,
  date: ObservationSortField.DATE,
};

export interface ObservationSort {
  field: ObservationSortField;
  direction: 'asc' | 'desc';
}

export enum ObservationGroupBy {
  GROUP = 'OBSERVATIONS_GROUP',
  REPORTED_BY = 'OBSERVATIONS_REPORTED_BY',
  ASSIGNED_GROUP = 'OBSERVATIONS_ASSIGNED_GROUP',
  SCORE = 'OBSERVATIONS_SCORE',
  STATUS = 'OBSERVATIONS_STATUS',
  LOCATION = 'OBSERVATIONS_LOCATION',
  ROOT_LOCATION = 'OBSERVATIONS_ROOT_LOCATION',
  CATEGORY = 'OBSERVATIONS_CATEGORY',
  CAUSE = 'OBSERVATIONS_CAUSE',
  DATE_WEEK = 'OBSERVATIONS_DATE_WEEK',
}

export const ObservationGroupByDisplayMap: Record<ObservationGroupBy, string> = {
  [ObservationGroupBy.SCORE]: 'Score',
  [ObservationGroupBy.STATUS]: 'Status',
  [ObservationGroupBy.GROUP]: 'Group',
  [ObservationGroupBy.REPORTED_BY]: 'User',
  [ObservationGroupBy.ASSIGNED_GROUP]: 'Assigned group',
  [ObservationGroupBy.LOCATION]: 'Location',
  [ObservationGroupBy.ROOT_LOCATION]: 'Root Location',
  [ObservationGroupBy.CATEGORY]: 'Category',
  [ObservationGroupBy.CAUSE]: 'Cause',
  [ObservationGroupBy.DATE_WEEK]: 'Observation week',
};

export const CreationDateWeekMeta = createDynamicMetaMap<string, EnumMetaItem>((iso) => ({
  label: getWeekNumberAndYear(iso),
  color: themeToColor('primary.main'),
}));

export interface ObservationGroupByFieldType {
  [ObservationGroupBy.SCORE]: SafetyWalkScore;
  [ObservationGroupBy.STATUS]: ObservationStatus;
  [ObservationGroupBy.DATE_WEEK]: string;
}

export const ObservationGroupByFieldMetaMap: {
  [K in keyof ObservationGroupByFieldType]: EnumMetaMap<ObservationGroupByFieldType[K]>;
} = {
  [ObservationGroupBy.SCORE]: SafetyWalkScoreMeta,
  [ObservationGroupBy.STATUS]: ObservationStatusMeta,
  [ObservationGroupBy.DATE_WEEK]: CreationDateWeekMeta,
};

export const ObservationGroupByFieldSortFunctionMap = {
  [ObservationGroupBy.DATE_WEEK]: sortDates,
};
