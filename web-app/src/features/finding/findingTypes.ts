import { Dayjs } from 'dayjs';
import { FileDisplay } from '../file/fileTypes';
import { GroupDisplay, GroupListRead, GroupRead } from '../group/groupTypes';
import { User, UserDisplay } from '../user/userTypes';
import { NormRequirementRead } from '../norm/normRequirementTypes';
import { ExternalAuditDisplay } from '../auditexternal/externalAuditTypes';
import { FindingScore, FindingStatus } from './sharedFindingTypes';
import { InternalAuditDisplay } from '../auditinternal/internalAuditTypes';
import { themeToColor } from '../../theme';
import { EnumMetaItem, EnumMetaMap } from '../../types';
import { createDynamicMetaMap, getWeekNumberAndYear, sortDates } from '../../utils';

export interface FindingParams {
  search?: string;
  statusNot?: string;
  status?: FindingStatus;
  score?: FindingScore;
  createdBy?: number;
  filter?: string;
  candidateGroups?: FindingCandidateGroups[];
  groupId?: number;
  ancestorId?: number;
  pageNumber?: number;
  pageSize?: number;
}

export interface FindingViewState {
  listView: 'open' | 'all';
  score?: FindingScore;
  group?: GroupListRead;
  status?: FindingStatus;
  createdBy?: User;
  candidateGroups?: FindingCandidateGroups[];
  search?: string;
}

export interface FindingPDFParams {
  id: number;
  timeZone: string;
}

export interface FindingCreate {
  group: number;
  date: number;
  score: FindingScore;
  description: string;
  files: number[];
}

export interface FindingUpdate {
  id: number;
  date: number;
  score: FindingScore;
  description: string;
  files: number[];
}

export interface FindingRead {
  id: number;
  sid: number;
  description: string;
  normParagraph: NormRequirementRead;
  files: FileDisplay[];
  group: GroupRead;
  date: number;
  score: FindingScore;
  createdAt: number;
  modifiedAt: number;
  createdBy: UserDisplay;
  modifiedBy: UserDisplay;
  status: FindingStatus;
  processInstanceId: string;
  externalAudit?: ExternalAuditDisplay | null;
  internalAudit?: InternalAuditDisplay | null;
  locked: boolean;
}

export const FindingScoreDisplayMap: Record<FindingScore, string> = {
  CONFORM: 'Conform',
  OPPORTUNITY_FOR_IMPROVEMENT: 'Opportunity for improvement',
  NON_CONFORMITY: 'Non-conformity',
  NA: 'Not applicable',
};

export const FindingScoreColorMap: Record<FindingScore, string> = {
  CONFORM: 'primary',
  OPPORTUNITY_FOR_IMPROVEMENT: 'warning.light',
  NON_CONFORMITY: 'error.main',
  NA: 'disabled.main',
};

export const FindingStatusDisplayMap: Record<FindingStatus, string> = {
  DRAFT: 'Draft',
  REPORTED: 'Reported',
  RESOLVED: 'Resolved',
  CANCELED: 'Canceled',
};

export interface FindingFormInput {
  group: GroupDisplay | null;
  description: string;
  score: FindingScore | null;
  date: Dayjs | null;
  files: FileDisplay[];
}

export interface FindingChange {
  by: UserDisplay;
  at: number;
  type: FindingChangeType;
  oldEntity: FindingRead;
  newEntity: FindingRead;
}

export enum FindingChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export const FindingChangeTypeDisplayMap: Record<FindingChangeType, string> = {
  INSERT: 'Finding reported',
  UPDATE: 'Finding updated',
  DELETE: 'Finding deleted',
};

export enum FindingCandidateGroups {
  FINDING_RESOLVE = 'FINDING_RESOLVE',
}

export const FindingCandidateGroupDisplayMap: Record<FindingCandidateGroups, string> = {
  FINDING_RESOLVE: 'Resolve',
};

export interface FindingState {
  findingViewState: FindingViewState;
}

export enum FindingGroupBy {
  GROUP = 'FINDINGS_GROUP',
  DATE_WEEK = 'FINDINGS_DATE_WEEK',
}

export const FindingGroupByDisplayMap: Record<FindingGroupBy, string> = {
  [FindingGroupBy.GROUP]: 'Group',
  [FindingGroupBy.DATE_WEEK]: 'Finding week',
};

export const CreationDateWeekMeta = createDynamicMetaMap<string, EnumMetaItem>((iso) => ({
  label: getWeekNumberAndYear(iso),
  color: themeToColor('primary.main'),
}));

export interface FindingGroupByFieldType {
  [FindingGroupBy.DATE_WEEK]: string;
}

export const FindingGroupByFieldMetaMap: {
  [K in keyof FindingGroupByFieldType]: EnumMetaMap<FindingGroupByFieldType[K]>;
} = {
  [FindingGroupBy.DATE_WEEK]: CreationDateWeekMeta,
};

export const FindingGroupByFieldSortFunctionMap = {
  [FindingGroupBy.DATE_WEEK]: sortDates,
};
