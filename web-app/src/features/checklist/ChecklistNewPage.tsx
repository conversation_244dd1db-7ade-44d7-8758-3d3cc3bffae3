/* eslint-disable react/jsx-no-useless-fragment */
import {
  Autocomplete,
  Box,
  FormControl,
  Grid,
  Paper,
  styled,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from '@mui/material';
import { useParams, useNavigate } from 'react-router-dom';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';
import { LoadingButton } from '@mui/lab';
import { useEffect, useState } from 'react';
import PageTitle from '../title/Title';
import { ChecklistAnswerFormInput, ChecklistFormInput } from './checklistTypes';
import ChecklistQuestionBlock from './ChecklistQuestionBlock';
import { preventSubmitOnEnter } from '../../utils';
import { useGetChecklistQuestionsQuery, useGetChecklistTemplatesQuery } from './template/checklistTemplateApi';
import { Answer } from './answer/checklistAnswerTypes';
import { useCreateChecklistMutation } from './checklistApi';
import UserSelect from '../user/UserSelect';
import UnsavedChangesDialog from '../../components/UnsavedChangesDialog';
import { ChecklistDoneBefore } from './template/checklistTemplateTypes';
import { useGetCurrentUserQuery } from '../user/userApi';

const schema = yup.object({
  checklistTemplate: yup.object().nullable().required().label('Checklist template'),
  answers: yup.array().of(
    yup.object().shape({
      question: yup.object(),
      answer: yup.string(),
      description: yup.string().when('scheduled', {
        is: true,
        then: yup.string().required().label('Description'),
        otherwise: yup.string().label('Description'),
      }),
    })
  ),
  assignee: yup.object().when('scheduled', {
    is: true,
    then: yup.object().nullable().required().label('Assignee'),
    otherwise: yup.object().nullable().label('Assignee'),
  }),
  scheduled: yup.boolean().label('Scheduled'),
});

const defaultSchema = {
  checklistTemplate: null,
  answers: [],
  assignee: null,
  scheduled: true,
};

const StyledToggleButtonGroup = styled(ToggleButtonGroup)(({ theme }) => ({
  borderRadius: '50px',
  border: `1px solid ${theme.palette.grey[300]}`,
  padding: '2px',
}));

const StyledToggleButton = styled(ToggleButton)(({ theme }) => ({
  border: 'none',
  padding: '10px 20px',
  borderRadius: '50px',
  '&.Mui-selected': {
    backgroundColor: theme.palette.background.paper,
    border: `1px solid ${theme.palette.grey[400]}`,
  },
  '&.Mui-selected:hover': {
    backgroundColor: theme.palette.background.paper,
  },
}));

function ChecklistNewPage() {
  const { changeId } = useParams();
  const navigate = useNavigate();
  const { data: me } = useGetCurrentUserQuery();
  const { data: checklists, isLoading: checklistsLoading } = useGetChecklistTemplatesQuery({
    enabled: true,
    pageSize: 25,
  });
  const [checklistTemplateId, setChecklistTemplateId] = useState<number>();
  const {
    handleSubmit,
    control,
    setValue,
    getValues,
    resetField,
    reset,
    formState: { isDirty },
  } = useForm<ChecklistFormInput>({
    resolver: yupResolver(schema),
    defaultValues: { ...defaultSchema, assignee: me },
  });

  const { data: questions } = useGetChecklistQuestionsQuery(
    { checklistTemplateId: Number(checklistTemplateId), enabled: true, pageSize: 100 },
    { skip: !checklistTemplateId }
  );
  const [createChangeChecklist, result] = useCreateChecklistMutation();

  useEffect(() => {
    if (questions && checklistTemplateId) {
      const questionsContent = [...questions.content];
      const answers: ChecklistAnswerFormInput[] = questionsContent.map((q) => ({
        answer: Answer.NA,
        question: q,
        description: '',
        files: [],
        doneBefore: q.doneBefore || ChecklistDoneBefore.CLOSE,
        assignee: null,
      }));
      setValue('answers', answers, { shouldDirty: true });
    } else if (questions && !checklistTemplateId) {
      setValue('answers', [], { shouldDirty: true });
    }
  }, [questions, setValue, checklistTemplateId]);

  const onSubmit = (form: ChecklistFormInput): void => {
    reset(form);
    const formAnswers = form.answers.map((o) => ({
      answer: o.answer,
      question: o.question.id,
      description: o.description,
      files: o.files.map((f) => f.id),
      assignee: o.assignee?.id,
      doneBefore: o.doneBefore,
    }));
    if (changeId !== null && checklistTemplateId !== null) {
      createChangeChecklist({
        change: Number(changeId),
        checklistTemplate: Number(checklistTemplateId),
        answers: formAnswers,
        assignee: form.assignee?.id,
        scheduled: form.scheduled,
      })
        .unwrap()
        .then(() => navigate('./../../'));
    }
  };

  const [scheduled, setScheduled] = useState(true);

  return (
    <form id="check-list-form" role="presentation" onSubmit={handleSubmit(onSubmit)} onKeyDown={preventSubmitOnEnter}>
      <PageTitle page="Add checklist" />
      <Typography variant="h5">Add checklist to change</Typography>
      <Grid spacing={1} container>
        <Grid item xs={12} md={12} lg={9} xl={8}>
          <Box display="flex" justifyContent="center" flexDirection="row" width="100%" sx={{ mb: 2 }}>
            <Controller
              name="scheduled"
              control={control}
              render={({ field }) => (
                <FormControl component="fieldset" margin="normal">
                  <StyledToggleButtonGroup
                    size="small"
                    value={field.value ? 'Schedule' : 'Complete now'}
                    exclusive
                    onChange={(event, newValue) => {
                      if (newValue === null) return; // Prevent toggle-off behavior

                      if (newValue === 'Complete now') {
                        resetField('assignee');
                      }

                      if (newValue === 'Schedule') {
                        const currentAnswers: ChecklistAnswerFormInput[] = getValues('answers');
                        const resetAnswers: ChecklistAnswerFormInput[] = currentAnswers.map((currentAnswer) => ({
                          ...currentAnswer,
                          answer: Answer.NA,
                          description: '',
                        }));

                        setValue('answers', resetAnswers);
                      }

                      const isScheduled = newValue === 'Schedule';
                      field.onChange(isScheduled);
                      setScheduled(isScheduled);
                    }}
                    aria-label="view toggle"
                  >
                    <StyledToggleButton value="Schedule" aria-label="Schedule">
                      Schedule for later
                    </StyledToggleButton>
                    <StyledToggleButton value="Complete now" aria-label="Complete now">
                      Complete now
                    </StyledToggleButton>
                  </StyledToggleButtonGroup>
                </FormControl>
              )}
            />
          </Box>

          <Paper sx={{ px: { xs: 2, md: 4 }, py: 2, borderRadius: 1 }} elevation={4}>
            <Controller
              name="checklistTemplate"
              control={control}
              render={({ field, fieldState: { error } }) => (
                <Autocomplete
                  id="checklistTemplate"
                  options={checklists?.content || []}
                  loading={checklistsLoading}
                  getOptionLabel={(option) => `${option.name}`}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  value={field.value}
                  filterOptions={(x) => x}
                  noOptionsText="No checklists"
                  fullWidth
                  onChange={(_, newValue) => {
                    field.onChange(newValue);
                    setChecklistTemplateId(newValue && newValue !== null ? newValue.id : undefined);
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Checklist template"
                      margin="normal"
                      fullWidth
                      size="small"
                      error={!!error}
                      helperText={error?.message}
                    />
                  )}
                />
              )}
            />
            {scheduled && (
              <Controller
                name="assignee"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <UserSelect label="Assignee" field={field} error={error} margin="normal" />
                )}
              />
            )}
          </Paper>
        </Grid>
        <UnsavedChangesDialog isDirty={isDirty} />
        {checklistTemplateId && (
          <Grid item xs={12} md={12} lg={9} xl={8}>
            {!scheduled && (
              <Controller
                name="answers"
                control={control}
                render={({ field }) => (
                  <>
                    {field.value !== null && <ChecklistQuestionBlock canEdit answers={field.value} control={control} />}
                  </>
                )}
              />
            )}
            <Box display="flex" justifyContent="flex-end" gap={2} mt={3}>
              <LoadingButton loading={result.isLoading} id="submit" variant="contained" type="submit" size="large">
                Add checklist
              </LoadingButton>
            </Box>
          </Grid>
        )}
      </Grid>
    </form>
  );
}

export default ChecklistNewPage;
