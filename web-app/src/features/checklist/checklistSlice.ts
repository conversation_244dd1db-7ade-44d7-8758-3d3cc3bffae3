import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { ChecklistColumnDefaults, ChecklistState, ChecklistViewState } from './checklistTypes';

const initialState: ChecklistState = {
  checklistViewState: {
    listView: 'open',
    columns: ChecklistColumnDefaults,
  }
};

export const checklistSlice = createSlice({
  name: 'checklist',
  initialState,
  reducers: {
    setChecklistViewState: (state, action: PayloadAction<ChecklistViewState>) => {
      state.checklistViewState = action.payload;
    },
  },
});

export const { setChecklistViewState } = checklistSlice.actions;

export const checklistReducer = persistReducer(
  {
    key: 'checklist',
    storage,
    whitelist: ['checklistViewState'],
  },
  checklistSlice.reducer
);
