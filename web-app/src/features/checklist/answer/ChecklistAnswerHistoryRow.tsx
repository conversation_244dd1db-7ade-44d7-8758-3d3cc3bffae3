import { Box, Collapse, IconButton, Table, TableBody, TableCell, TableHead, TableRow, Typography } from '@mui/material';
import { useState } from 'react';
import PersonIcon from '@mui/icons-material/Person';
import DnsIcon from '@mui/icons-material/Dns';
import DateRangeIcon from '@mui/icons-material/DateRange';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import { AnswerDisplayMap, ChecklistAnswerRead, IssueStatus } from './checklistAnswerTypes';
import { ChecklistDoneBeforeDisplayMap } from '../template/checklistTemplateTypes';

interface ChecklistAnswerHistoryRowProps {
  date: number;
  actionBy: string;
  action: string;
  oldEntity?: ChecklistAnswerRead;
  newEntity?: ChecklistAnswerRead;
}

function ChecklistAnswerHistoryRow({ date, actionBy, action, oldEntity, newEntity }: ChecklistAnswerHistoryRowProps) {
  const [open, setOpen] = useState<boolean>(false);

  // Get differences between relations
  const removedDocuments = oldEntity?.files?.filter((o) => !newEntity?.files?.find((n) => o.id === n.id));
  const addedDocuments = newEntity?.files?.filter((o) => !oldEntity?.files?.find((n) => o.id === n.id));

  // Check for service tasks
  let newAction = action;
  let newIcon = <PersonIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />;
  let newActionBy = actionBy;
  let showUpdate = !!(oldEntity || newEntity);
  newAction = !oldEntity?.locked && newEntity?.locked ? 'Checklist locked' : newAction;
  newAction =
    oldEntity?.status === IssueStatus.DRAFT && newEntity?.status === IssueStatus.ACTIVE ? 'Issue activated' : newAction;
  newAction =
    oldEntity?.status !== IssueStatus.RESOLVED && newEntity?.status === IssueStatus.RESOLVED
      ? 'Issue resolved'
      : newAction;
  newAction =
    oldEntity?.status !== IssueStatus.CANCELED && newEntity?.status === IssueStatus.CANCELED
      ? 'Issue canceled'
      : newAction;
  newIcon =
    newAction !== action &&
    !(oldEntity?.status !== IssueStatus.CANCELED && newEntity?.status === IssueStatus.CANCELED) ? (
      <DnsIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />
    ) : (
      newIcon
    );
  newActionBy =
    newAction !== action && !(oldEntity?.status !== IssueStatus.CANCELED && newEntity?.status === IssueStatus.CANCELED)
      ? 'System'
      : newActionBy;
  showUpdate = newAction !== action ? false : showUpdate;

  return (
    <>
      <TableRow>
        <TableCell>
          <DateRangeIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />{' '}
          {new Date(date).toLocaleTimeString([], {
            day: '2-digit',
            month: '2-digit',
            year: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
          })}
        </TableCell>
        <TableCell>
          {newIcon} {newActionBy}
        </TableCell>
        <TableCell>{newAction}</TableCell>
        <TableCell padding="checkbox">
          {showUpdate && (
            <IconButton aria-label="expand row" size="small" onClick={() => setOpen(!open)}>
              {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
            </IconButton>
          )}
        </TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0, border: !open ? 0 : undefined }} colSpan={6}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 1 }}>
              <Typography variant="h6" gutterBottom component="div">
                Changes
              </Typography>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Field</TableCell>
                    <TableCell>Removed</TableCell>
                    <TableCell>Added</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {(oldEntity?.description || newEntity?.description) && (
                    <TableRow>
                      <TableCell>Description</TableCell>
                      <TableCell sx={{ whiteSpace: 'normal' }}>{oldEntity?.description}</TableCell>
                      <TableCell sx={{ whiteSpace: 'normal' }}>{newEntity?.description}</TableCell>
                    </TableRow>
                  )}
                  {(oldEntity?.assignee || newEntity?.assignee) && (
                    <TableRow>
                      <TableCell>Assignee</TableCell>
                      <TableCell sx={{ whiteSpace: 'normal' }}>
                        {oldEntity?.assignee && `${oldEntity.assignee.firstName} ${oldEntity.assignee.lastName}`}
                      </TableCell>
                      <TableCell sx={{ whiteSpace: 'normal' }}>
                        {newEntity?.assignee && `${newEntity.assignee.firstName} ${newEntity.assignee.lastName}`}
                      </TableCell>
                    </TableRow>
                  )}
                  {(oldEntity?.doneBefore || newEntity?.doneBefore) && (
                    <TableRow>
                      <TableCell>Completion phase</TableCell>
                      <TableCell>
                        {oldEntity?.doneBefore && ChecklistDoneBeforeDisplayMap[oldEntity.doneBefore]}
                      </TableCell>
                      <TableCell>
                        {newEntity?.doneBefore && ChecklistDoneBeforeDisplayMap[newEntity.doneBefore]}
                      </TableCell>
                    </TableRow>
                  )}
                  {(oldEntity?.answer || newEntity?.answer) && (
                    <TableRow>
                      <TableCell>Answer</TableCell>
                      <TableCell sx={{ whiteSpace: 'normal' }}>
                        {oldEntity?.answer && AnswerDisplayMap[oldEntity?.answer]}
                      </TableCell>
                      <TableCell sx={{ whiteSpace: 'normal' }}>
                        {newEntity?.answer && AnswerDisplayMap[newEntity?.answer]}
                      </TableCell>
                    </TableRow>
                  )}
                  {(oldEntity?.files || newEntity?.files) && (
                    <TableRow>
                      <TableCell sx={{ verticalAlign: 'top' }}>Documents</TableCell>
                      <TableCell sx={{ verticalAlign: 'top' }}>
                        {removedDocuments && removedDocuments.map((f) => <div>{f?.name}</div>)}
                      </TableCell>
                      <TableCell sx={{ verticalAlign: 'top' }}>
                        {addedDocuments && addedDocuments.map((f) => <div>{f?.name}</div>)}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
      <TableRow />
    </>
  );
}
export default ChecklistAnswerHistoryRow;
