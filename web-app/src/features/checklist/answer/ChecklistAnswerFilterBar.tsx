import {
  <PERSON>complete,
  <PERSON><PERSON>,
  Box,
  <PERSON><PERSON>,
  debounce,
  IconButton,
  Paper,
  Stack,
  styled,
  TextField,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import FilterListIcon from '@mui/icons-material/FilterList';
import { useEffect, useMemo, useState } from 'react';
import { IssueStatusDisplayMap, IssueStatus, ChecklistAnswerViewState } from './checklistAnswerTypes';
import { useGetGroupsQuery } from '../../group/groupApi';
import FormDialog from '../../../components/FormDialog';
import { ChecklistDoneBefore, ChecklistDoneBeforeDisplayMap } from '../template/checklistTemplateTypes';
import { useGetUsersQuery } from '../../user/userApi';
import { UserType } from '../../user/userTypes';

const StyledTextField = styled(TextField)(() => ({
  '& .MuiInput-underline:before': { borderBottom: 'none' },
  '& .MuiInput-underline:after': { borderBottom: 'none' },
  '& .MuiInput-root:hover:before': { borderBottom: 'none !important' },
}));

const StyledAutoComplete = styled(Autocomplete)(({ theme }) => ({
  [theme.breakpoints.down('lg')]: {
    display: 'none',
  },
})) as typeof Autocomplete;

const StyledIconButton = styled(IconButton)(({ theme }) => ({
  [theme.breakpoints.up('lg')]: {
    display: 'none',
  },
})) as typeof IconButton;

interface ChecklistAnswerFilterBarProps {
  groupId: number;
  resetPageNumber: () => void;
  value: ChecklistAnswerViewState;
  onChange: (newValue: ChecklistAnswerViewState) => void;
}

function ChecklistAnswerFilterBar({ groupId, resetPageNumber, value, onChange }: ChecklistAnswerFilterBarProps) {
  const theme = useTheme();
  const smallScreen = useMediaQuery(theme.breakpoints.down('lg'));
  const [loadReportedGroups, setLoadReportedGroups] = useState<boolean>(false);
  const [loadAssignees, setLoadAssignees] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>(value.search || '');
  const defaultPageSize = 1000;

  const { data: reportedGroups, isLoading: reportedGroupLoading } = useGetGroupsQuery(
    { checklistAnswerAncestorGroupId: groupId },
    { skip: !loadReportedGroups }
  );

  const { data: assignees, isLoading: assigneesLoading } = useGetUsersQuery(
    {
      checklistAnswerAssignedByGroupId: Number(groupId),
      pageSize: defaultPageSize,
      type: UserType.PERSON,
    },
    { skip: !loadAssignees }
  );

  const reportedGroupOptions = reportedGroups || [];
  const assigneeOptions = assignees?.content || [];

  const [filterDialogOpen, setFilterDialogOpen] = useState<boolean>(false);
  const [dialogParams, setDialogParams] = useState<ChecklistAnswerViewState>(value);
  const hasFilters = value.group || value.status;

  useEffect(() => {
    setDialogParams(value);
  }, [value]);

  const handleDelayedSearch = useMemo(
    () =>
      debounce((search: string, state: ChecklistAnswerViewState) => {
        const newState = { ...state };
        newState.search = search && search.length > 0 ? search : undefined;
        onChange(newState);
        resetPageNumber();
      }, 200),
    [resetPageNumber, onChange]
  );

  const handleSearch = (search: string): void => {
    setSearchValue(search);
    handleDelayedSearch(search, value);
  };

  const clearSearch = (): void => {
    setSearchValue('');
    const newState = { ...value };
    newState.search = undefined;
    onChange(newState);
    resetPageNumber();
  };

  const onDialogSubmit = (): void => {
    onChange(dialogParams);
    resetPageNumber();
    setFilterDialogOpen(false);
  };

  const onDialogClose = (): void => {
    setFilterDialogOpen(false);
    setDialogParams(value);
  };

  const onDialogClear = (): void => {
    const newParams = { ...dialogParams };
    newParams.group = undefined;
    newParams.status = undefined;
    newParams.doneBefore = undefined;
    newParams.assignee = undefined;
    setDialogParams(newParams);
  };

  return (
    <Paper elevation={4}>
      <Stack py={1} px={2} direction="row">
        <StyledTextField
          sx={{
            flexGrow: 1,
            '.clearIndicator': { visibility: 'hidden' },
            ':hover .clearIndicator': { visibility: 'visible' },
            ':focus-within .clearIndicator': { visibility: 'visible' },
          }}
          InputProps={{
            startAdornment: <SearchIcon sx={{ color: 'rgba(0, 0, 0, 0.57)', mr: 1 }} fontSize="small" />,
            endAdornment: searchValue.length > 0 && (
              <IconButton size="small" onClick={clearSearch} className="clearIndicator">
                <ClearIcon fontSize="small" />
              </IconButton>
            ),
          }}
          placeholder="Search by ID or description"
          margin="none"
          variant="standard"
          size="small"
          value={searchValue}
          onChange={(e) => handleSearch(e.target.value)}
        />
        <StyledAutoComplete
          id="reportedGroup"
          options={reportedGroupOptions}
          getOptionLabel={(option) => option.name}
          isOptionEqualToValue={(option, v) => option.id === v.id}
          value={value.group || null}
          onFocus={() => setLoadReportedGroups(true)}
          loading={reportedGroupLoading}
          onChange={(_, newValue) => {
            const newState = { ...value };
            const newGroup = newValue != null ? newValue : undefined;
            newState.group = newGroup;
            onChange(newState);
            resetPageNumber();
          }}
          componentsProps={{
            popper: {
              sx: {
                minWidth: 'fit-content',
              },
            },
          }}
          renderInput={(fieldParams) => (
            <StyledTextField
              {...fieldParams}
              placeholder="Group"
              margin="none"
              variant="standard"
              size="small"
              sx={{ minWidth: '155px' }}
            />
          )}
        />
        <StyledAutoComplete
          id="assignee"
          options={assigneeOptions}
          getOptionLabel={(option) => option.fullName}
          isOptionEqualToValue={(option, v) => option.id === v.id}
          value={value.assignee || null}
          onFocus={() => setLoadAssignees(true)}
          loading={assigneesLoading}
          onChange={(_, newValue) => {
            const newState = { ...value };
            const newUser = newValue != null ? newValue : undefined;
            newState.assignee = newUser;
            onChange(newState);
            resetPageNumber();
          }}
          componentsProps={{
            popper: {
              sx: {
                minWidth: 'fit-content',
              },
            },
          }}
          renderInput={(fieldParams) => (
            <StyledTextField
              {...fieldParams}
              placeholder="Assignee"
              margin="none"
              variant="standard"
              size="small"
              sx={{ minWidth: '140px' }}
            />
          )}
        />
        <StyledAutoComplete
          id="doneBefore"
          options={Object.values(ChecklistDoneBefore)}
          getOptionLabel={(option) => ChecklistDoneBeforeDisplayMap[option as ChecklistDoneBefore]}
          value={value.doneBefore || null}
          onChange={(_, newValue) => {
            const newState = { ...value };
            newState.doneBefore = newValue != null ? newValue : undefined;
            onChange(newState);
            resetPageNumber();
          }}
          renderInput={(fieldParams) => (
            <StyledTextField
              {...fieldParams}
              placeholder="Completion phase"
              margin="none"
              variant="standard"
              size="small"
              sx={{ minWidth: '140px' }}
            />
          )}
        />
        <StyledAutoComplete
          id="status"
          options={Object.values(IssueStatus)}
          getOptionLabel={(option) => IssueStatusDisplayMap[option as IssueStatus]}
          value={value?.status ? value.status : null}
          onChange={(_, newValue) => {
            const newState = { ...value };
            newState.status = newValue != null ? newValue : undefined;
            onChange(newState);
            resetPageNumber();
          }}
          renderInput={(fieldParams) => (
            <StyledTextField
              {...fieldParams}
              placeholder="Status"
              margin="none"
              variant="standard"
              size="small"
              sx={{ minWidth: '140px' }}
            />
          )}
        />
        <StyledIconButton size="small" onClick={() => setFilterDialogOpen(true)}>
          <Badge variant="dot" color="primary" invisible={!hasFilters}>
            <FilterListIcon fontSize="small" />
          </Badge>
        </StyledIconButton>
      </Stack>
      <FormDialog
        open={smallScreen && filterDialogOpen}
        dialogTitleText="Filter issues"
        submitButtonText="Apply filters"
        onClose={onDialogClose}
        onSubmit={onDialogSubmit}
      >
        <Autocomplete
          id="reportedGroup"
          options={reportedGroupOptions}
          getOptionLabel={(option) => option.name}
          isOptionEqualToValue={(option, v) => option.id === v.id}
          value={dialogParams.group || null}
          onFocus={() => setLoadReportedGroups(true)}
          loading={reportedGroupLoading}
          onChange={(_, newValue) => {
            const newParams = { ...dialogParams };
            const newGroup = newValue != null ? newValue : undefined;
            newParams.group = newGroup;
            setDialogParams(newParams);
          }}
          renderInput={(fieldParams) => (
            <TextField {...fieldParams} placeholder="Group" margin="normal" variant="outlined" size="small" fullWidth />
          )}
          slotProps={{
            popper: {
              sx: {
                minWidth: 'fit-content',
              },
            },
          }}
        />
        <Autocomplete
          id="assignee"
          options={assigneeOptions || []}
          getOptionLabel={(option) => option.fullName}
          isOptionEqualToValue={(option, v) => option.id === v.id}
          value={value.assignee || null}
          onFocus={() => setLoadAssignees(true)}
          loading={assigneesLoading}
          onChange={(_, newValue) => {
            const newState = { ...value };
            newState.assignee = newValue || undefined;
            onChange(newState);
            resetPageNumber();
          }}
          renderInput={(params) => (
            <TextField {...params} placeholder="Assignee" margin="normal" variant="outlined" size="small" fullWidth />
          )}
        />
        <Autocomplete
          id="doneBefore"
          options={Object.values(ChecklistDoneBefore)}
          getOptionLabel={(option) => ChecklistDoneBeforeDisplayMap[option]}
          value={dialogParams.doneBefore || null}
          onChange={(_, newValue) => {
            const newParams = { ...dialogParams };
            newParams.doneBefore = newValue != null ? newValue : undefined;
            setDialogParams(newParams);
          }}
          renderInput={(fieldParams) => (
            <TextField
              {...fieldParams}
              placeholder="Completion phase"
              margin="normal"
              variant="outlined"
              size="small"
              fullWidth
            />
          )}
        />
        <Autocomplete
          id="status"
          options={Object.values(IssueStatus)}
          getOptionLabel={(option) => IssueStatusDisplayMap[option]}
          value={dialogParams?.status ? dialogParams.status : null}
          onChange={(_, newValue) => {
            const newParams = { ...dialogParams };
            newParams.status = newValue != null ? newValue : undefined;
            setDialogParams(newParams);
          }}
          renderInput={(fieldParams) => (
            <TextField
              {...fieldParams}
              placeholder="Status"
              margin="normal"
              variant="outlined"
              size="small"
              fullWidth
            />
          )}
        />
        <Box mt={2}>
          <Button variant="outlined" fullWidth onClick={onDialogClear}>
            Clear all filters
          </Button>
        </Box>
      </FormDialog>
    </Paper>
  );
}
export default ChecklistAnswerFilterBar;
