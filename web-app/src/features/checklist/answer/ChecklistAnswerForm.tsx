import { <PERSON><PERSON>, <PERSON>Field, Box, Autocomplete } from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect } from 'react';
import dayjs from 'dayjs';
import { ChecklistAnswerFormInput } from './checklistAnswerTypes';
import { preventSubmitOnEnter } from '../../../utils';
import FileUpload from '../../file/FileUpload';
import UnsavedChangesDialog from '../../../components/UnsavedChangesDialog';
import { ChecklistDoneBefore, ChecklistDoneBeforeDisplayMap } from '../template/checklistTemplateTypes';
import UserSelect from '../../user/UserSelect';

dayjs.Ls.en.weekStart = 1;

const schema = yup.object({
  description: yup.string().required().label('Description'),
  doneBefore: yup.mixed().oneOf(Object.values(ChecklistDoneBefore)).nullable().required().label('Completion phase'),
  files: yup.array().label('Documents'),
  assignee: yup.object().nullable().label('Assignee'),
});

const defaultSchema = {
  description: '',
  files: [],
  doneBefore: null,
  assignee: null,
};

interface ChecklistAnswerFormProps {
  onDirtyChange?: (isDirty: boolean) => void;
  defaultValues?: Partial<ChecklistAnswerFormInput>;
  onSubmit: (form: ChecklistAnswerFormInput) => void;
  isEditing?: boolean;
  submitError: boolean;
  locked?: boolean;
  canEdit?: boolean;
  submitButton?: React.ReactElement;
  groupId?: number;
}

function ChecklistAnswerForm({
  onDirtyChange,
  defaultValues,
  onSubmit,
  submitError,
  submitButton,
  isEditing,
  groupId,
  locked = false,
  canEdit = true,
}: ChecklistAnswerFormProps) {
  const readOnly = locked || !canEdit;
  const {
    handleSubmit,
    control,
    reset,
    formState: { isDirty },
  } = useForm<ChecklistAnswerFormInput>({
    resolver: yupResolver(schema),
    defaultValues: { ...defaultSchema, ...defaultValues },
  });

  useEffect(() => {
    if (onDirtyChange) {
      onDirtyChange(isDirty);
    }
  }, [isDirty, onDirtyChange]);

  useEffect(() => {
    // reset form if new default values are present
    reset({ ...defaultSchema, ...defaultValues });
  }, [defaultValues, reset]);

  return (
    <form
      id="checklistAnswer-form"
      role="presentation"
      onSubmit={handleSubmit(onSubmit)}
      onKeyDown={preventSubmitOnEnter}
    >
      <Controller
        name="description"
        control={control}
        render={({ field, fieldState: { error } }) => (
          <TextField
            name={field.name}
            ref={field.ref}
            onChange={field.onChange}
            onBlur={field.onBlur}
            value={field.value}
            label="Description"
            size="small"
            fullWidth
            multiline
            minRows={2}
            autoComplete="off"
            margin="normal"
            error={!!error}
            helperText={error?.message}
            slotProps={{
              htmlInput: {
                readOnly,
                maxLength: 510,
              },

              inputLabel: {
                shrink: isEditing,
              },
            }}
          />
        )}
      />
      <Box
        display="flex"
        flex="1"
        gap={{ xs: 0, sm: 2 }}
        flexWrap={{ xs: 'wrap', sm: 'unset' }}
        flexDirection={{ xs: 'column', sm: 'row' }}
      >
        <Box flex={1} display="flex" flexDirection="column">
          <Controller
            name="assignee"
            control={control}
            render={({ field, fieldState: { error } }) => (
              <UserSelect
                label="Assignee (optional)"
                field={field}
                assignedGroup={groupId}
                error={error}
                margin="normal"
                readOnly={readOnly}
              />
            )}
          />
        </Box>
        <Box flex={1}>
          <Controller
            name="doneBefore"
            control={control}
            render={({ field, fieldState: { error: fieldError } }) => (
              <Autocomplete
                {...field}
                value={field.value ? field.value : null}
                onChange={(_, newValue) => field.onChange(newValue)}
                options={Object.values(ChecklistDoneBefore)}
                getOptionLabel={(option) => ChecklistDoneBeforeDisplayMap[option as ChecklistDoneBefore] || ''}
                isOptionEqualToValue={(option, value) => option === value}
                readOnly={readOnly}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Completion phase"
                    size="small"
                    fullWidth
                    margin="normal"
                    error={!!fieldError}
                    helperText={fieldError?.message}
                  />
                )}
              />
            )}
          />
        </Box>
      </Box>
      <Controller
        name="files"
        control={control}
        render={({ field }) => (
          <FileUpload label="Documents" readOnly={readOnly} onChange={field.onChange} value={field.value} />
        )}
      />
      <UnsavedChangesDialog isDirty={isDirty} />
      {submitError && (
        <Alert severity="error" sx={{ mt: 1 }}>
          Something went wrong on our servers
        </Alert>
      )}
      <Box display="flex" justifyContent="flex-end" mt={3}>
        {submitButton}
      </Box>
    </form>
  );
}
export default ChecklistAnswerForm;
