import { Tab<PERSON><PERSON>x<PERSON>, TabPanel } from '@mui/lab';
import {
  Box,
  Button,
  Link,
  Paper,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
} from '@mui/material';
import DateRangeIcon from '@mui/icons-material/DateRange';
import ChecklistIcon from '@mui/icons-material/ChecklistOutlined';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import LockIcon from '@mui/icons-material/Lock';
import PersonIcon from '@mui/icons-material/Person';
import AddIcon from '@mui/icons-material/Add';
import { useEffect, useState } from 'react';
import { Link as RouterLink, useParams } from 'react-router-dom';
import { useGetChecklistAnswersQuery } from './checklistAnswerApi';
import { ChecklistAnswerParams, IssueStatusDisplayMap } from './checklistAnswerTypes';
import TableCellLink from '../../../components/TableCellLink';
import ErrorGate from '../../../components/ErrorGate';
import ChecklistAnswerFilterBar from './ChecklistAnswerFilterBar';
import ChecklistAnswerChipFilter from './ChecklistAnswerChipFilter';
import { useGetCurrentUserQuery } from '../../user/userApi';
import { useAppDispatch, useAppSelector } from '../../../store';
import { setChecklistAnswerTabViewState } from './checklistAnswerSlice';
import { ChecklistDoneBeforeDisplayMap } from '../template/checklistTemplateTypes';
import { GuardResult } from '../../guard/guardHooks';
import { PermissionType } from '../../guard/guardTypes';
import { UserRole } from '../../user/userTypes';
import Guard from '../../guard/Guard';

interface ChecklistAnswerListTabProps {
  changeId: number;
  readOnly?: boolean;
}

function ChecklistAnswerListTab({ changeId, readOnly = false }: ChecklistAnswerListTabProps) {
  const { groupId } = useParams();
  const { data: me } = useGetCurrentUserQuery();
  const checklistAnswerTabViewState = useAppSelector((state) => state.checklistAnswer.checklistAnswerTabViewState);
  const dispatch = useAppDispatch();
  const defaultPageSize = 25;

  const [params, setParams] = useState<ChecklistAnswerParams>({
    pageSize: defaultPageSize,
    pageNumber: 0,
    groupId: checklistAnswerTabViewState.group?.id,
    ancestorId: Number(groupId),
    status: checklistAnswerTabViewState.status,
    createdBy: checklistAnswerTabViewState.createdBy?.id,
    search: checklistAnswerTabViewState.search,
    candidateGroups: checklistAnswerTabViewState.candidateGroups,
    doneBefore: checklistAnswerTabViewState.doneBefore,
    assignee: checklistAnswerTabViewState.assignee?.id,
    changeId,
  });
  const { data, isLoading, error } = useGetChecklistAnswersQuery(params);
  const [page, setPage] = useState<number>(0);
  const canCreateIssue = (guard: GuardResult) =>
    guard.hasRole(UserRole.TENANT_ADMIN) || guard.hasPermission(PermissionType.CHANGE_CREATE);

  // ? Avoid a layout jump when reaching the last page with empty rows.
  const emptyRows = page > 0 ? (data?.pageSize || 0) - (data?.content.length || 0) : 0;

  useEffect(() => {
    if (checklistAnswerTabViewState) {
      setParams((prev) => ({
        ...prev,
        groupId: checklistAnswerTabViewState.group?.id,
        status: checklistAnswerTabViewState.status,
        createdBy: checklistAnswerTabViewState.createdBy?.id,
        search: checklistAnswerTabViewState.search,
        candidateGroups: checklistAnswerTabViewState.candidateGroups,
        doneBefore: checklistAnswerTabViewState.doneBefore,
        assignee: checklistAnswerTabViewState.assignee?.id,
        changeId,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [checklistAnswerTabViewState]);

  const handleChangePage = (newPage: number) => {
    setPage(newPage);
    const newParams = { ...params };
    newParams.pageNumber = newPage;
    setParams(newParams);
  };

  const resetPageNumber = (): void => {
    handleChangePage(0);
  };

  return (
    <ErrorGate error={error}>
      <TabContext value="0">
        <Box display="flex" flexDirection="row" justifyContent="space-between" mb={1}>
          <ChecklistAnswerChipFilter
            me={me}
            resetPageNumber={resetPageNumber}
            value={checklistAnswerTabViewState}
            onChange={(v) => dispatch(setChecklistAnswerTabViewState(v))}
          />
          {!readOnly && (
            <Guard hasAccess={canCreateIssue}>
              <Button
                component={RouterLink}
                to={`./../../changes/${changeId}/issues/add`}
                size="medium"
                variant="outlined"
                endIcon={<AddIcon />}
              >
                Add issue
              </Button>
            </Guard>
          )}
        </Box>
        <ChecklistAnswerFilterBar
          groupId={Number(groupId)}
          resetPageNumber={resetPageNumber}
          value={checklistAnswerTabViewState}
          onChange={(v) => dispatch(setChecklistAnswerTabViewState(v))}
        />
        <TabPanel sx={{ px: 0, pt: 1, pb: 2 }} value="0">
          <TableContainer component={Paper} elevation={4}>
            <Table sx={{ whiteSpace: 'nowrap' }}>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ width: '75px' }}>ID</TableCell>
                  <TableCell sx={{ maxWidth: '700px' }}>Description</TableCell>
                  <TableCell sx={{ width: '200' }}>Assignee</TableCell>
                  <TableCell sx={{ width: '250px' }}>Checklist</TableCell>
                  <TableCell sx={{ width: '130px' }}>Completion phase</TableCell>
                  <TableCell sx={{ width: '130px' }}>Status</TableCell>
                  <TableCell sx={{ width: '178px' }}>Date</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {!isLoading &&
                  data &&
                  data.content.length > 0 &&
                  data.content.map((p) => (
                    <TableRow key={p.id} hover>
                      <TableCellLink to={`./../../issues/${p.id}`} sx={{ maxWidth: '75px' }}>
                        {p.sid}
                      </TableCellLink>
                      <TableCellLink to={`./../../issues/${p.id}`} sx={{ maxWidth: { xs: '350px', lg: '500px' } }}>
                        {p.description}
                      </TableCellLink>
                      <TableCellLink to={`./../../issues/${p.id}`} sx={{ maxWidth: '200px' }}>
                        {p.assignee && (
                          <>
                            <PersonIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} /> {p.assignee.fullName}
                          </>
                        )}
                      </TableCellLink>
                      <TableCellLink to={`./../../issues/${p.id}`} sx={{ maxWidth: '250px' }}>
                        {p?.checklist && (
                          <>
                            <ChecklistIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />{' '}
                            <Link underline="hover" component={RouterLink} to={`./../checklists/${p.checklist.id}`}>
                              #{p.checklist.sid}
                            </Link>{' '}
                            {p.checklist.checklistTemplate?.name}
                          </>
                        )}
                      </TableCellLink>
                      <TableCellLink to={`./../../issues/${p.id}`} tableCellProps={{ sx: { width: '130' } }}>
                        {ChecklistDoneBeforeDisplayMap[p.doneBefore]}
                      </TableCellLink>
                      <TableCellLink to={`./../../issues/${p.id}`} tableCellProps={{ sx: { width: '130' } }}>
                        {p.locked ? (
                          <LockIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />
                        ) : (
                          <LockOpenIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />
                        )}{' '}
                        {IssueStatusDisplayMap[p.status]}
                      </TableCellLink>
                      <TableCellLink to={`./../../issues/${p.id}`} tableCellProps={{ sx: { width: '130px' } }}>
                        <DateRangeIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />{' '}
                        {new Date(p.creationDate).toDateString()}{' '}
                      </TableCellLink>
                    </TableRow>
                  ))}
                {!isLoading && (!data || data.content.length === 0) && (
                  <TableRow>
                    <TableCell align="center" colSpan={7}>
                      No issues found
                    </TableCell>
                  </TableRow>
                )}
                {isLoading && (
                  <TableRow>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                  </TableRow>
                )}
                {emptyRows > 0 && (
                  <TableRow style={{ height: 53 * emptyRows }}>
                    <TableCell colSpan={7} />
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <Box display="flex" justifyContent="flex-end">
            <TablePagination
              component="div"
              sx={{ border: 0 }}
              rowsPerPage={defaultPageSize}
              rowsPerPageOptions={[]}
              count={!isLoading && data ? data.total : 0}
              page={page}
              onPageChange={(_, pageNumber) => handleChangePage(pageNumber)}
            />
          </Box>
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}

export default ChecklistAnswerListTab;
