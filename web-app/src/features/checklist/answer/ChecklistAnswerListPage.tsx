import { Tab<PERSON><PERSON>x<PERSON>, TabPanel } from '@mui/lab';
import {
  Box,
  Link,
  Paper,
  Skeleton,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Tabs,
} from '@mui/material';
import DateRangeIcon from '@mui/icons-material/DateRange';
import PeopleAltOutlinedIcon from '@mui/icons-material/PeopleAltOutlined';
import SyncIcon from '@mui/icons-material/SyncOutlined';
import ChecklistIcon from '@mui/icons-material/ChecklistOutlined';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import LockIcon from '@mui/icons-material/Lock';
import PersonIcon from '@mui/icons-material/Person';
import { useEffect, useState } from 'react';
import { Link as RouterLink, useParams } from 'react-router-dom';
import { useGetChecklistAnswersQuery } from './checklistAnswerApi';
import { ChecklistAnswerParams, IssueStatus, IssueStatusDisplayMap } from './checklistAnswerTypes';
import TableCellLink from '../../../components/TableCellLink';
import ErrorGate from '../../../components/ErrorGate';
import PageTitle from '../../title/Title';
import ChecklistAnswerFilterBar from './ChecklistAnswerFilterBar';
import ChecklistAnswerChipFilter from './ChecklistAnswerChipFilter';
import { useGetCurrentUserQuery } from '../../user/userApi';
import { useAppDispatch, useAppSelector } from '../../../store';
import { setChecklistAnswerViewState } from './checklistAnswerSlice';
import { ChecklistDoneBeforeDisplayMap } from '../template/checklistTemplateTypes';

function ChecklistAnswerListPage() {
  const { groupId } = useParams();
  const { data: me } = useGetCurrentUserQuery();
  const checklistAnswerViewState = useAppSelector((state) => state.checklistAnswer.checklistAnswerViewState);
  const dispatch = useAppDispatch();
  const defaultPageSize = 25;

  const getFilter = (view?: 'open' | 'all') => {
    const usedView = view || checklistAnswerViewState.listView;
    if (usedView === 'all') {
      return `ancestorId=${groupId}`;
    }

    return `
      status=${IssueStatus.ACTIVE}
      &
      ancestorId=${groupId}
    `
      .replace(/\n/g, '')
      .replace(/\s/g, '');
  };

  const [params, setParams] = useState<ChecklistAnswerParams>({
    pageSize: defaultPageSize,
    pageNumber: 0,
    groupId: checklistAnswerViewState.group?.id,
    status: checklistAnswerViewState.status,
    createdBy: checklistAnswerViewState.createdBy?.id,
    search: checklistAnswerViewState.search,
    candidateGroups: checklistAnswerViewState.candidateGroups,
    doneBefore: checklistAnswerViewState.doneBefore,
    assignee: checklistAnswerViewState.assignee?.id,
    filter: getFilter(),
  });
  const { data, isLoading, error } = useGetChecklistAnswersQuery(params);
  const [page, setPage] = useState<number>(0);

  // ? Avoid a layout jump when reaching the last page with empty rows.
  const emptyRows = page > 0 ? (data?.pageSize || 0) - (data?.content.length || 0) : 0;

  useEffect(() => {
    if (checklistAnswerViewState) {
      setParams((prev) => ({
        ...prev,
        groupId: checklistAnswerViewState.group?.id,
        status: checklistAnswerViewState.status,
        createdBy: checklistAnswerViewState.createdBy?.id,
        search: checklistAnswerViewState.search,
        candidateGroups: checklistAnswerViewState.candidateGroups,
        doneBefore: checklistAnswerViewState.doneBefore,
        assignee: checklistAnswerViewState.assignee?.id,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [checklistAnswerViewState]);

  const onTabSwitch = (view: 'open' | 'all') => {
    dispatch(
      setChecklistAnswerViewState({
        ...checklistAnswerViewState,
        listView: view,
      })
    );
    const newParams = { ...params };
    newParams.pageNumber = 0;
    newParams.filter = getFilter(view);
    setParams(newParams);
  };

  const handleChangePage = (newPage: number) => {
    setPage(newPage);
    const newParams = { ...params };
    newParams.pageNumber = newPage;
    setParams(newParams);
  };

  const resetPageNumber = (): void => {
    handleChangePage(0);
  };

  return (
    <ErrorGate error={error}>
      <PageTitle page="Issues" />
      <TabContext value="0">
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Tabs value={checklistAnswerViewState.listView} sx={{ width: 'fit-content' }}>
            <Tab label="Open" value="open" onClick={() => onTabSwitch('open')} />
            <Tab label="All" value="all" onClick={() => onTabSwitch('all')} />
          </Tabs>
        </Box>
        <ChecklistAnswerChipFilter
          me={me}
          resetPageNumber={resetPageNumber}
          value={checklistAnswerViewState}
          onChange={(v) => dispatch(setChecklistAnswerViewState(v))}
        />
        <ChecklistAnswerFilterBar
          groupId={Number(groupId)}
          resetPageNumber={resetPageNumber}
          value={checklistAnswerViewState}
          onChange={(v) => dispatch(setChecklistAnswerViewState(v))}
        />
        <TabPanel sx={{ px: 0, pt: 1, pb: 2 }} value="0">
          <TableContainer component={Paper} elevation={4}>
            <Table sx={{ whiteSpace: 'nowrap' }}>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ width: '75px' }}>ID</TableCell>
                  <TableCell sx={{ maxWidth: '500px' }}>Description</TableCell>
                  <TableCell sx={{ width: '200px' }}>Group</TableCell>
                  <TableCell sx={{ width: '200px' }}>Assignee</TableCell>
                  <TableCell sx={{ width: '250px' }}>Checklist</TableCell>
                  <TableCell sx={{ width: '250px' }}>Change</TableCell>
                  <TableCell sx={{ width: '130px' }}>Completion phase</TableCell>
                  <TableCell sx={{ width: '130px' }}>Status</TableCell>
                  <TableCell sx={{ width: '178px' }}>Date</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {!isLoading &&
                  data &&
                  data.content.length > 0 &&
                  data.content.map((p) => (
                    <TableRow key={p.id} hover>
                      <TableCellLink to={`${p.id}`} sx={{ maxWidth: '75px' }}>
                        {p.sid}
                      </TableCellLink>
                      <TableCellLink to={`${p.id}`} sx={{ maxWidth: { xs: '350px', lg: '500px' } }}>
                        {p.description}
                      </TableCellLink>
                      <TableCellLink to={`${p.id}`} sx={{ maxWidth: '200px' }}>
                        <PeopleAltOutlinedIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} /> {p.group.name}
                      </TableCellLink>
                      <TableCellLink to={`${p.id}`} sx={{ maxWidth: '200px' }}>
                        {p.assignee && (
                          <>
                            <PersonIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} /> {p.assignee.fullName}
                          </>
                        )}
                      </TableCellLink>
                      <TableCellLink to={`${p.id}`} sx={{ maxWidth: '250px' }}>
                        {p?.checklist && (
                          <>
                            <ChecklistIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />{' '}
                            <Link underline="hover" component={RouterLink} to={`./../checklists/${p.checklist.id}`}>
                              #{p.checklist.sid}
                            </Link>{' '}
                            {p.checklist.checklistTemplate?.name}
                          </>
                        )}
                      </TableCellLink>
                      <TableCellLink to={`${p.id}`} sx={{ maxWidth: '250px' }}>
                        <SyncIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />{' '}
                        <Link underline="hover" component={RouterLink} to={`./../changes/${p?.change?.id}`}>
                          #{p?.change?.sid}
                        </Link>{' '}
                        {p?.change?.title}
                      </TableCellLink>
                      <TableCellLink to={`${p.id}`} sx={{ maxWidth: '130px' }}>
                        {ChecklistDoneBeforeDisplayMap[p.doneBefore]}
                      </TableCellLink>
                      <TableCellLink to={`${p.id}`} tableCellProps={{ sx: { width: '130' } }}>
                        {p.locked ? (
                          <LockIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />
                        ) : (
                          <LockOpenIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />
                        )}{' '}
                        {IssueStatusDisplayMap[p.status]}
                      </TableCellLink>
                      <TableCellLink to={`${p.id}`} tableCellProps={{ sx: { width: '130px' } }}>
                        <DateRangeIcon fontSize="small" sx={{ verticalAlign: 'text-top' }} />{' '}
                        {new Date(p.creationDate).toDateString()}{' '}
                      </TableCellLink>
                    </TableRow>
                  ))}
                {!isLoading && (!data || data.content.length === 0) && (
                  <TableRow>
                    <TableCell align="center" colSpan={9}>
                      No issues found
                    </TableCell>
                  </TableRow>
                )}
                {isLoading && (
                  <TableRow>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                  </TableRow>
                )}
                {emptyRows > 0 && (
                  <TableRow style={{ height: 53 * emptyRows }}>
                    <TableCell colSpan={9} />
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <Box display="flex" justifyContent="flex-end">
            <TablePagination
              component="div"
              sx={{ border: 0 }}
              rowsPerPage={defaultPageSize}
              rowsPerPageOptions={[]}
              count={!isLoading && data ? data.total : 0}
              page={page}
              onPageChange={(_, pageNumber) => handleChangePage(pageNumber)}
            />
          </Box>
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}

export default ChecklistAnswerListPage;
