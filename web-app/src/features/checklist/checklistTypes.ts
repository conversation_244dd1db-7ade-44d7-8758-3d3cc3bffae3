import { GroupDisplay, GroupListRead } from '../group/groupTypes';
import { User, UserDisplay } from '../user/userTypes';
import { Answer, IssueStatus } from './answer/checklistAnswerTypes';
import {
  ChecklistQuestionDisplay,
  ChecklistDoneBefore,
  ChecklistTemplateRead,
} from './template/checklistTemplateTypes';
import { ChangeDisplay } from '../change/changeTypes';
import { FileDisplay } from '../file/fileTypes';
import { themeToColor } from '../../theme';
import { EnumMetaItem, EnumMetaMap } from '../../types';
import { createDynamicMetaMap, getWeekNumberAndYear, sortDates } from '../../utils';

export interface ChecklistDisplay {
  id: number;
  sid: number;
  change: ChangeDisplay;
  checklistTemplate: ChecklistTemplateRead;
}
export interface ChecklistParams {
  createdBy?: number;
  assignee?: number;
  ancestorGroupId?: number;
  groupId?: number;
  changeId?: number;
  status?: ChecklistStatus;
  search?: string;
  sort?: string;
  pageSize?: number;
  pageNumber?: number;
  filter?: string;
}

export interface ChecklistPDFParams {
  id: number;
  timeZone: string;
}

export interface ChecklistRead {
  id: number;
  sid: number;
  processInstanceId: string;
  assignee: UserDisplay;
  status: ChecklistStatus;
  change: ChangeDisplay;
  answers: ChecklistAnswerRead[];
  group: GroupDisplay;
  checklistTemplate: ChecklistTemplateRead;
  resolvedIssuesCount: number;
  issuesTotalCount: number;
  createdAt: number;
  modifiedAt: number;
  createdBy: UserDisplay;
  modifiedBy: UserDisplay;
  locked: boolean;
}

export interface ChecklistCreate {
  scheduled: boolean;
  change: number;
  checklistTemplate: number;
  answers: ChecklistAnswerCreate[];
  assignee?: number;
}

export interface ChecklistUpdate {
  id: number;
  answers: ChecklistAnswerUpdate[];
}

export interface ChecklistAnswerCreate {
  question: number;
  answer: Answer;
  description?: string;
  files: number[];
  doneBefore?: ChecklistDoneBefore;
  assignee?: number;
}

export interface ChecklistAnswerUpdate {
  id?: number;
  answer: Answer;
  description?: string;
  files: number[];
  doneBefore?: ChecklistDoneBefore;
  assignee?: number;
}

export interface ChecklistAnswerRead {
  id: number;
  sid?: number;
  answer: Answer;
  processInstanceId: string;
  status: IssueStatus;
  question: ChecklistQuestionDisplay;
  description?: string;
  files: FileDisplay[];
  locked: boolean;
  doneBefore: ChecklistDoneBefore;
  assignee?: UserDisplay;
}

export enum ChecklistStatus {
  TODO = 'TODO',
  ACTIVE = 'ACTIVE',
  RESOLVED = 'RESOLVED',
  CANCELED = 'CANCELED',
}

export const ChecklistStatusDisplayMap: Record<ChecklistStatus, string> = {
  TODO: 'To do',
  ACTIVE: 'Active',
  RESOLVED: 'Resolved',
  CANCELED: 'Canceled',
};

export interface ChecklistFormInput {
  scheduled: boolean;
  group: GroupDisplay | null;
  checklistTemplate: ChecklistTemplateRead | null;
  answers: ChecklistAnswerFormInput[];
  assignee: UserDisplay | null;
}

export interface ChecklistAnswerFormInput {
  answerRead?: ChecklistAnswerRead;
  question: ChecklistQuestionDisplay;
  answer: Answer;
  description: string;
  files: FileDisplay[];
  doneBefore?: ChecklistDoneBefore;
  assignee: UserDisplay | null;
}

export interface ChecklistViewState {
  listView: 'open' | 'all';
  group?: GroupListRead;
  createdBy?: User;
  assignee?: User;
  status?: ChecklistStatus;
  search?: string;
  sort?: ChecklistSort[];
  columns?: ChecklistColumnSetting[];
}

export interface ChecklistState {
  checklistViewState: ChecklistViewState;
}

export enum ChecklistChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export const ChecklistChangeTypeDisplayMap: Record<ChecklistChangeType, string> = {
  INSERT: 'Checklist created',
  UPDATE: 'Checklist updated',
  DELETE: 'Checklist deleted',
};

export interface ChecklistChange {
  by: UserDisplay;
  at: number;
  type: ChecklistChangeType;
  oldEntity: ChecklistRead;
  newEntity: ChecklistRead;
}

export enum ChecklistGroupBy {
  GROUP = 'CHECKLISTS_GROUP',
  START_DATE_WEEK = 'CHECKLISTS_START_DATE_WEEK',
}

export const ChecklistGroupByDisplayMap: Record<ChecklistGroupBy, string> = {
  [ChecklistGroupBy.GROUP]: 'Group',
  [ChecklistGroupBy.START_DATE_WEEK]: 'Checklist week',
};

export const CreationDateWeekMeta = createDynamicMetaMap<string, EnumMetaItem>((iso) => ({
  label: getWeekNumberAndYear(iso),
  color: themeToColor('primary.main'),
}));

export interface ChecklistGroupByFieldType {
  [ChecklistGroupBy.START_DATE_WEEK]: string; // ISO date strings
}

export const ChecklistGroupByFieldMetaMap: {
  [K in keyof ChecklistGroupByFieldType]: EnumMetaMap<ChecklistGroupByFieldType[K]>;
} = {
  [ChecklistGroupBy.START_DATE_WEEK]: CreationDateWeekMeta,
};

export const ChecklistGroupByFieldSortFunctionMap = {
  [ChecklistGroupBy.START_DATE_WEEK]: sortDates,
};

export enum ChecklistSortField {
  SID = 'sid',
  TITLE = 'title',
  STATUS = 'status',
  CREATED_AT = 'created_at',
  MODIFIED_AT = 'modified_at',
}

export const ChecklistFieldSortMap: Partial<Record<keyof ChecklistRead, ChecklistSortField>> = {
  sid: ChecklistSortField.SID,
  status: ChecklistSortField.STATUS,
  createdAt: ChecklistSortField.CREATED_AT,
  modifiedAt: ChecklistSortField.MODIFIED_AT,
};

export interface ChecklistSort {
  field: ChecklistSortField;
  direction: 'asc' | 'desc';
}

export enum ChecklistColumn {
  SID = 'sid',
  TITLE = 'title',
  GROUP = 'group',
  ASSIGNEE = 'assignee',
  CHANGE = 'change',
  SUMMARY = 'summary',
  ISSUES = 'issues',
  STATUS = 'status',
}

export const ChecklistColumnDisplayMap: Record<ChecklistColumn, string> = {
  [ChecklistColumn.SID]: 'ID',
  [ChecklistColumn.TITLE]: 'Title',
  [ChecklistColumn.GROUP]: 'Group',
  [ChecklistColumn.ASSIGNEE]: 'Assignee',
  [ChecklistColumn.CHANGE]: 'Change',
  [ChecklistColumn.SUMMARY]: 'Summary',
  [ChecklistColumn.ISSUES]: 'Issues',
  [ChecklistColumn.STATUS]: 'Status',
};

export interface ChecklistColumnSetting {
  column: ChecklistColumn;
  hidden: boolean;
  width: number;
}

export const ChecklistColumnDefaults: ChecklistColumnSetting[] = [
  {
    column: ChecklistColumn.SID,
    hidden: false,
    width: 75,
  },
  {
    column: ChecklistColumn.TITLE,
    hidden: false,
    width: 250,
  },
  {
    column: ChecklistColumn.GROUP,
    hidden: false,
    width: 200,
  },
  {
    column: ChecklistColumn.ASSIGNEE,
    hidden: false,
    width: 200,
  },
  {
    column: ChecklistColumn.CHANGE,
    hidden: false,
    width: 250,
  },
  {
    column: ChecklistColumn.SUMMARY,
    hidden: false,
    width: 170,
  },
  {
    column: ChecklistColumn.ISSUES,
    hidden: false,
    width: 100,
  },
  {
    column: ChecklistColumn.STATUS,
    hidden: false,
    width: 135,
  },
];
