import { <PERSON><PERSON>, <PERSON>complete, Checkbox, FormControlLabel, TextField } from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import FormDialog from '../../../components/FormDialog';
import {
  ChecklistQuestionCreate,
  ChecklistDoneBefore,
  ChecklistDoneBeforeDisplayMap,
  ChecklistQuestionFormInput,
  ChecklistQuestionRead,
} from './checklistTemplateTypes';

const schema = yup.object({
  name: yup.string().required().label('Question'),
  order: yup.number().nullable().required().label('Order'),
  enabled: yup.boolean().label('Enabled'),
  doneBefore: yup.string().nullable().label('Completion phase'),
});

interface ChecklistQuestionFormDialogProps {
  checklistQuestion?: ChecklistQuestionRead;
  checklistTemplate: number;
  open: boolean;
  error?: string;
  disabled?: boolean;
  dialogTitleText: string;
  dialogContentText?: string;
  submitButtonText?: string;
  onClose: () => void;
  onSubmit: (ChecklistQuestionCreate: ChecklistQuestionCreate) => void;
}

function ChecklistQuestionFormDialog({
  checklistQuestion,
  checklistTemplate,
  open = false,
  error,
  disabled = false,
  dialogTitleText,
  dialogContentText,
  submitButtonText,
  onClose,
  onSubmit,
}: ChecklistQuestionFormDialogProps) {
  const questionToQuestionForm = (question: ChecklistQuestionRead): ChecklistQuestionFormInput => {
    const { name, order, enabled, doneBefore } = question;
    return { name, order, enabled, checklistTemplate, doneBefore };
  };
  const questionFormToQuestionCreate = (question: ChecklistQuestionFormInput): ChecklistQuestionCreate => {
    const { name, order, enabled, doneBefore } = question;
    return { name, order, enabled, checklistTemplate, doneBefore };
  };

  const { handleSubmit, control } = useForm<ChecklistQuestionFormInput>({
    resolver: yupResolver(schema),
    defaultValues: checklistQuestion !== undefined ? questionToQuestionForm(checklistQuestion) : undefined,
  });

  return (
    <FormDialog
      open={open}
      disabled={disabled}
      dialogTitleText={dialogTitleText}
      dialogContentText={dialogContentText}
      submitButtonText={submitButtonText}
      onClose={onClose}
      onSubmit={handleSubmit((f) => onSubmit(questionFormToQuestionCreate(f)))}
    >
      <Controller
        name="name"
        defaultValue=""
        control={control}
        render={({ field, fieldState: { error: fieldError } }) => (
          <TextField
            name={field.name}
            ref={field.ref}
            onChange={field.onChange}
            onBlur={field.onBlur}
            value={field.value}
            label="Question"
            size="small"
            fullWidth
            autoComplete="off"
            margin="normal"
            error={!!fieldError}
            helperText={fieldError?.message}
            slotProps={{
              htmlInput: {
                maxLength: 510,
              },
            }}
          />
        )}
      />
      <Controller
        name="doneBefore"
        defaultValue={ChecklistDoneBefore.CLOSE}
        control={control}
        render={({ field, fieldState: { error: fieldError } }) => (
          <Autocomplete
            {...field}
            onChange={(_, newValue) => field.onChange(newValue)}
            options={Object.keys(ChecklistDoneBeforeDisplayMap)}
            getOptionLabel={(option) => ChecklistDoneBeforeDisplayMap[option as ChecklistDoneBefore]}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Completion phase"
                size="small"
                fullWidth
                margin="normal"
                error={!!fieldError}
                helperText={fieldError?.message}
              />
            )}
          />
        )}
      />
      <Controller
        name="order"
        defaultValue={1}
        control={control}
        render={({ field, fieldState: { error: fieldError } }) => (
          <TextField
            name={field.name}
            ref={field.ref}
            onChange={field.onChange}
            onBlur={field.onBlur}
            value={field.value}
            label="Order"
            size="small"
            type="number"
            fullWidth
            autoComplete="off"
            margin="normal"
            error={!!fieldError}
            helperText={fieldError?.message}
          />
        )}
      />
      <Controller
        name="enabled"
        defaultValue
        control={control}
        render={({ field }) => (
          <FormControlLabel
            sx={{ my: 1 }}
            label="Enabled"
            control={
              <Checkbox
                name={field.name}
                ref={field.ref}
                onChange={field.onChange}
                onBlur={field.onBlur}
                value={field.value}
                checked={field.value}
              />
            }
          />
        )}
      />
      {error && (
        <Alert severity="error" sx={{ mt: 1 }}>
          Something went wrong on our servers
        </Alert>
      )}
    </FormDialog>
  );
}

export default ChecklistQuestionFormDialog;
