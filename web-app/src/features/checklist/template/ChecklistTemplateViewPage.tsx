import {
  Box,
  Paper,
  Skeleton,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Typography,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import DoNotDisturbOnIcon from '@mui/icons-material/DoNotDisturbOn';
import { LoadingButton, TabContext, TabPanel } from '@mui/lab';
import { useNavigate, useParams } from 'react-router-dom';
import { useState } from 'react';
import { useGetChecklistQuestionsQuery, useGetChecklistTemplateQuery } from './checklistTemplateApi';
import EditChecklistTemplateDialog from './EditChecklistTemplateDialog';
import ChecklistTemplateListActions from './ChecklistTemplateListActions';
import { ChecklistDoneBeforeDisplayMap, ChecklistQuestionRead, ChecklistTemplateRead } from './checklistTemplateTypes';
import NewChecklistQuestionDialog from './NewChecklistQuestionDialog';
import EditChecklistQuestionDialog from './EditChecklistQuestionDialog';
import ChecklistQuestionListActions from './ChecklistQuestionListActions';
import DeleteChecklistTemplateDialog from './DeleteChecklistTemplateDialog';
import DeleteChecklistQuestionDialog from './DeleteChecklistQuestionDialog';
import ErrorGate from '../../../components/ErrorGate';
import PageTitle from '../../title/Title';

function ChecklistTemplateViewPage() {
  const { checklistTemplateId } = useParams();
  const navigate = useNavigate();
  const { data: checklistTemplate, error } = useGetChecklistTemplateQuery(Number(checklistTemplateId), {
    skip: !checklistTemplateId,
  });
  const { data: questions, isLoading } = useGetChecklistQuestionsQuery(
    { checklistTemplateId: Number(checklistTemplateId), pageSize: 100 },
    { skip: !checklistTemplateId }
  );
  const [editChecklistTemplate, setEditChecklistTemplate] = useState<ChecklistTemplateRead>();
  const [deleteChecklistTemplate, setDeleteChecklistTemplate] = useState<ChecklistTemplateRead>();
  const [questionOpen, setQuestionOpen] = useState<boolean>(false);
  const [editQuestion, setEditQuestion] = useState<ChecklistQuestionRead>();
  const [deleteQuestion, setDeleteQuestion] = useState<ChecklistQuestionRead>();

  return (
    <ErrorGate error={error}>
      <PageTitle
        page={checklistTemplate ? `${checklistTemplate?.name} - Checklist templates` : 'Checklist templates'}
      />
      <TabContext value="checklistTemplate">
        <Box>
          <Box display="flex" justifyContent="space-between" flexWrap="wrap">
            <Typography variant="h5" sx={{ mb: 1, width: { xs: '100%', md: 'unset' } }}>
              {checklistTemplate?.name}
            </Typography>
            <Box display="flex" gap={2} flexWrap="wrap" mb={{ xs: 1, md: 0 }}>
              <Box width={{ xs: '100%', sm: 'unset' }}>
                <LoadingButton
                  fullWidth
                  variant="contained"
                  type="button"
                  onClick={() => setQuestionOpen(true)}
                  endIcon={<AddIcon />}
                >
                  New question
                </LoadingButton>
              </Box>
              <Box>
                <ChecklistTemplateListActions
                  id="checklistTemplate-view"
                  onEdit={() => setEditChecklistTemplate(checklistTemplate)}
                  onDelete={() => setDeleteChecklistTemplate(checklistTemplate)}
                />
              </Box>
            </Box>
          </Box>
          <Box display="flex" gap={1} flexWrap="wrap">
            <Box display="inline" whiteSpace="nowrap">
              {checklistTemplate &&
                (checklistTemplate.enabled ? (
                  <>
                    <CheckCircleIcon fontSize="small" color="success" sx={{ verticalAlign: 'text-bottom' }} /> Enabled
                  </>
                ) : (
                  <>
                    <DoNotDisturbOnIcon fontSize="small" color="disabled" sx={{ verticalAlign: 'text-bottom' }} />{' '}
                    Disabled
                  </>
                ))}
            </Box>
          </Box>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Tabs value="checklistTemplate" sx={{ borderBottom: '1px solid lightgray', width: '100%' }}>
              <Tab sx={{ borderBottom: 0 }} label="Checklist template" value="checklistTemplate" />
            </Tabs>
          </Box>
        </Box>
        <TabPanel keepMounted value="checklistTemplate" sx={{ p: 0 }}>
          <TableContainer component={Paper} elevation={4}>
            <Table sx={{ whiteSpace: 'nowrap' }}>
              <TableHead>
                <TableRow>
                  <TableCell>Question</TableCell>
                  <TableCell>Completion phase</TableCell>
                  <TableCell>Order</TableCell>
                  <TableCell width="90">Enabled</TableCell>
                  <TableCell />
                </TableRow>
              </TableHead>
              <TableBody>
                {!isLoading &&
                  questions &&
                  questions.content.length > 0 &&
                  questions.content.map((q) => (
                    <TableRow key={q.id}>
                      <TableCell width="100%">{q.name}</TableCell>
                      <TableCell>{ChecklistDoneBeforeDisplayMap[q.doneBefore]}</TableCell>
                      <TableCell>{q.order}</TableCell>
                      <TableCell sx={{ py: 0, maxWidth: '90px' }}>
                        {q.enabled ? <CheckCircleIcon color="success" /> : <DoNotDisturbOnIcon color="disabled" />}
                      </TableCell>
                      <TableCell padding="checkbox" align="right">
                        <ChecklistQuestionListActions
                          id={q.id.toString()}
                          onEdit={() => setEditQuestion(q)}
                          onDelete={() => setDeleteQuestion(q)}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                {!isLoading && (!questions || questions.content.length === 0) && (
                  <TableRow>
                    <TableCell align="center" colSpan={4}>
                      No questions found
                    </TableCell>
                  </TableRow>
                )}
                {isLoading && (
                  <TableRow>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                    <TableCell>
                      <Skeleton />
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>
        {editChecklistTemplate && (
          <EditChecklistTemplateDialog
            open={!!editChecklistTemplate}
            checklistTemplate={editChecklistTemplate}
            onClose={() => setEditChecklistTemplate(undefined)}
          />
        )}
        {deleteChecklistTemplate && (
          <DeleteChecklistTemplateDialog
            open={!!deleteChecklistTemplate}
            checklistTemplate={deleteChecklistTemplate}
            onClose={() => setDeleteChecklistTemplate(undefined)}
            onDelete={() => navigate(`./../..`)}
          />
        )}
        {questionOpen && checklistTemplate && (
          <NewChecklistQuestionDialog
            checklistTemplate={checklistTemplate.id}
            open={questionOpen}
            onClose={() => setQuestionOpen(false)}
          />
        )}
        {editQuestion && checklistTemplate && (
          <EditChecklistQuestionDialog
            checklistTemplate={checklistTemplate.id}
            open={!!editQuestion}
            checklistQuestion={editQuestion}
            onClose={() => setEditQuestion(undefined)}
          />
        )}
        {deleteQuestion && checklistTemplate && (
          <DeleteChecklistQuestionDialog
            open={!!deleteQuestion}
            checklistQuestion={deleteQuestion}
            onClose={() => setDeleteQuestion(undefined)}
          />
        )}
      </TabContext>
    </ErrorGate>
  );
}
export default ChecklistTemplateViewPage;
