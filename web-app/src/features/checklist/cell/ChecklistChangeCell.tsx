import { GridRenderCellParams } from '@mui/x-data-grid';
import SyncIcon from '@mui/icons-material/SyncOutlined';
import { Link } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { ChecklistRead } from '../checklistTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

type ChecklistChangeCellParam = GridRenderCellParams<ChecklistRead>;

export default function ChecklistChangeCell(params: ChecklistChangeCellParam) {
  const { row } = params;
  const { change } = row;

  const displayText = `#${change.sid} ${change.title}`;

  return (
    <Cell title={displayText}>
      <SyncIcon fontSize="small" />{' '}
      <Link underline="hover" component={RouterLink} to={`./../changes/${change.id}`}>
        #{change.sid}
      </Link>{' '}
      <CellText>{change.title}</CellText>
    </Cell>
  );
}
