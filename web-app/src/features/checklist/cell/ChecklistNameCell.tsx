import { GridRenderCellParams } from '@mui/x-data-grid';
import { ChecklistRead } from '../checklistTypes';
import CellText from '../../../components/CellText';
import Cell from '../../../components/Cell';

type ChecklistNameCellParam = GridRenderCellParams<ChecklistRead, string>;

export default function ChecklistNameCell(params: ChecklistNameCellParam) {
  const { formattedValue: title } = params;

  if (!title || title.length === 0) {
    return null;
  }

  return (
    <Cell title={title}>
      <CellText>{title}</CellText>
    </Cell>
  );
}
