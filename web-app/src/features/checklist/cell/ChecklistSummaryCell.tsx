import { GridRenderCellParams } from '@mui/x-data-grid';
import { ChecklistRead } from '../checklistTypes';
import Cell from '../../../components/Cell';
import ChecklistAnswerSummary from '../ChecklistAnswerSummary';

type ChecklistSummaryCellParam = GridRenderCellParams<ChecklistRead>;

export default function ChecklistSummaryCell(params: ChecklistSummaryCellParam) {
  const { row } = params;

  return (
    <Cell>
      <ChecklistAnswerSummary checklist={row} />
    </Cell>
  );
}
