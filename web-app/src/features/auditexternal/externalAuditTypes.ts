import { Dayjs } from 'dayjs';
import { GroupDisplay, GroupListRead } from '../group/groupTypes';
import { FindingScore, FindingStatus } from '../finding/sharedFindingTypes';
import { User, UserDisplay } from '../user/userTypes';
import { FileDisplay } from '../file/fileTypes';
import { NormDisplay, NormRead } from '../norm/normTypes';
import { NormRequirementRead } from '../norm/normRequirementTypes';
import { themeToColor } from '../../theme';
import { EnumMetaItem, EnumMetaMap } from '../../types';
import { createDynamicMetaMap, getWeekNumberAndYear, sortDates } from '../../utils';

export interface ExternalAuditDisplay {
  id: number;
  sid: number;
  status: ExternalAuditStatus;
  locked: boolean;
}
export interface ExternalAuditParams {
  createdBy?: number;
  ancestorGroupId?: number;
  groupId?: number;
  status?: ExternalAuditStatus;
  candidateGroups?: string[];
  search?: string;
  pageSize?: number;
  pageNumber?: number;
  filter?: string;
  normIds?: number[];
}

export interface ExternalAuditPDFParams {
  id: number;
  timeZone: string;
}

export interface ExternalAuditViewState {
  listView: 'open' | 'all';
  group?: GroupListRead;
  status?: ExternalAuditStatus;
  createdBy?: User;
  search?: string;
  norms?: NormRead[];
}

export interface ExternalAuditRead {
  id: number;
  sid: number;
  summary: string;
  auditor: string;
  date: number;
  findings: ExternalAuditFindingRead[];
  norms: NormDisplay[];
  group: GroupDisplay;
  processInstanceId: string;
  status: ExternalAuditStatus;
  locked: boolean;
  createdAt: number;
  modifiedAt: number;
  createdBy: UserDisplay;
  modifiedBy: UserDisplay;
}

export interface ExternalAuditListRead {
  id: number;
  sid: number;
  date: number;
  group: GroupDisplay;
  status: ExternalAuditStatus;
  locked: boolean;
  createdAt: number;
  createdBy: UserDisplay;
}

export interface ExternalAuditCreate {
  summary: string;
  auditor: string;
  date: number;
  group: number;
  findings: ExternalAuditFindingCreate[];
  norms: number[];
}

export interface ExternalAuditUpdate {
  id: number;
  summary: string;
  auditor: string;
  date: number;
  findings: ExternalAuditFindingUpdate[];
  norms: number[];
}

export interface ExternalAuditDeletable {
  deletable: boolean;
}

export interface ExternalAuditFindingCreate {
  score: FindingScore;
  description: string;
  files: number[];
}

export interface ExternalAuditFindingUpdate {
  id?: number;
  score: FindingScore;
  description: string;
  files: number[];
}

export interface ExternalAuditFindingRead {
  id: number;
  sid: number;
  score: FindingScore;
  processInstanceId: string;
  normParagraph: NormRequirementRead;
  status: FindingStatus;
  description: string;
  files: FileDisplay[];
  locked: boolean;
  creationDate: number;
}

export enum ExternalAuditStatus {
  REPORTED = 'REPORTED',
  RESOLVED = 'RESOLVED',
  CANCELED = 'CANCELED',
}

export const ExternalAuditStatusDisplayMap: Record<ExternalAuditStatus, string> = {
  REPORTED: 'Reported',
  RESOLVED: 'Resolved',
  CANCELED: 'Canceled',
};

export interface ExternalAuditFormInput {
  group: GroupDisplay | null;
  date: Dayjs;
  summary: string;
  auditor: string;
  findings: ExternalAuditAnswerFormInput[];
  norms: NormDisplay[];
}

export interface ExternalAuditAnswerFormInput {
  findingRead?: ExternalAuditFindingRead;
  score: FindingScore;
  description: string;
  files: FileDisplay[];
  normParagraph?: NormRequirementRead;
  creationDate: number;
}

export interface ExternalAuditState {
  externalAuditViewState: ExternalAuditViewState;
}

export enum ExternalAuditChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export const ExternalAuditChangeTypeDisplayMap: Record<ExternalAuditChangeType, string> = {
  INSERT: 'External audit created',
  UPDATE: 'External audit updated',
  DELETE: 'External audit deleted',
};

export interface ExternalAuditChange {
  by: UserDisplay;
  at: number;
  type: ExternalAuditChangeType;
  oldEntity: ExternalAuditRead;
  newEntity: ExternalAuditRead;
}

export enum ExternalAuditGroupBy {
  GROUP = 'EXTERNAL_AUDITS_GROUP',
  DATE_WEEK = 'EXTERNAL_AUDITS_DATE_WEEK',
}

export const ExternalAuditGroupByDisplayMap: Record<ExternalAuditGroupBy, string> = {
  [ExternalAuditGroupBy.GROUP]: 'Group',
  [ExternalAuditGroupBy.DATE_WEEK]: 'External audit week',
};

export const CreationDateWeekMeta = createDynamicMetaMap<string, EnumMetaItem>((iso) => ({
  label: getWeekNumberAndYear(iso),
  color: themeToColor('primary.main'),
}));

export interface ExternalAuditGroupByFieldType {
  [ExternalAuditGroupBy.DATE_WEEK]: string;
}

export const ExternalAuditGroupByFieldMetaMap: {
  [K in keyof ExternalAuditGroupByFieldType]: EnumMetaMap<ExternalAuditGroupByFieldType[K]>;
} = {
  [ExternalAuditGroupBy.DATE_WEEK]: CreationDateWeekMeta,
};

export const ExternalAuditGroupByFieldSortFunctionMap = {
  [ExternalAuditGroupBy.DATE_WEEK]: sortDates,
};
