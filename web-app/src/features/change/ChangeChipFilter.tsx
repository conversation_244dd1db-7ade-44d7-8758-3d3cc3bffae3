import React, { useState, useEffect } from 'react';
import { <PERSON>, Stack, IconButton, Popover, FormControlLabel, Tooltip, Typography, Checkbox } from '@mui/material';
import TuneIcon from '@mui/icons-material/Tune';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import { useAppDispatch, useAppSelector } from '../../store';
import { setChangeViewState } from './changeSlice';
import { User } from '../user/userTypes';
import {
  ChangeCandidateGroup,
  ChangeCandidateGroupsDisplayMap,
  ChangeColumn,
  ChangeColumnDefaults,
  ChangeColumnDisplayMap,
  ChangeColumnSetting,
} from './changeTypes';

const mergeColumns = (
  persistedColumns: ChangeColumnSetting[] = [],
  defaultColumns: ChangeColumnSetting[] = []
): ChangeColumnSetting[] => {
  // Keep only the persisted columns that still exist in the defaults.
  const validPersisted = persistedColumns.filter((pc) => defaultColumns.some((dc) => dc.column === pc.column));

  // Create a set of the column keys in the valid persisted list.
  const persistedSet = new Set(validPersisted.map((pc) => pc.column));

  // Find any default columns that the customer hasn't chosen yet.
  const newDefaults = defaultColumns.filter((dc) => !persistedSet.has(dc.column));

  // Return the persisted order first, then add new columns at the end.
  return [...validPersisted, ...newDefaults];
};

interface ChangeChipFilterProps {
  me?: User;
  resetPageNumber: () => void;
}

function ChangeChipFilter({ me, resetPageNumber }: ChangeChipFilterProps) {
  const changeViewState = useAppSelector((state) => state.change.changeViewState);
  const dispatch = useAppDispatch();

  // Use merged columns: combine persisted columns with defaults.
  const initialColumnsOrder: ChangeColumnSetting[] = mergeColumns(changeViewState?.columns, ChangeColumnDefaults);
  const [columnsOrder, setColumnsOrder] = useState<ChangeColumnSetting[]>(initialColumnsOrder);

  useEffect(() => {
    if (changeViewState?.columns) {
      const mergedColumns = mergeColumns(changeViewState.columns, ChangeColumnDefaults);
      setColumnsOrder(mergedColumns);
      // Check if the merged result is different from the persisted state.
      if (JSON.stringify(mergedColumns) !== JSON.stringify(changeViewState.columns)) {
        dispatch(setChangeViewState({ ...changeViewState, columns: mergedColumns }));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [changeViewState?.columns]);

  // Popover state for the "View options" button.
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const handleFineTuneClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handlePopoverClose = () => {
    setAnchorEl(null);
  };
  const open = Boolean(anchorEl);
  const id = open ? 'fine-tune-popover' : undefined;

  // Local state to track drag index.
  const [draggingIndex, setDraggingIndex] = useState<number | null>(null);

  const handleDragStart = (index: number) => {
    setDraggingIndex(index);
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleDrop = (index: number) => {
    if (draggingIndex !== null && draggingIndex !== index) {
      const newOrder = [...columnsOrder];
      const [moved] = newOrder.splice(draggingIndex, 1);
      newOrder.splice(index, 0, moved);
      setColumnsOrder(newOrder);
      // Update view state with the new ordering.
      dispatch(setChangeViewState({ ...changeViewState, columns: newOrder }));
    }
    setDraggingIndex(null);
  };

  const handleToggleColumn = (column: ChangeColumn) => {
    // Toggle the hidden flag for the selected column.
    const newOrder = columnsOrder.map((setting) => {
      if (setting.column === column) {
        return { ...setting, hidden: !setting.hidden };
      }
      return setting;
    });
    setColumnsOrder(newOrder);
    dispatch(setChangeViewState({ ...changeViewState, columns: newOrder }));
  };

  return (
    <Stack direction="row" flexWrap={{ xs: 'nowrap', sm: 'wrap' }} overflow={{ xs: 'scroll', sm: 'unset' }}>
      <Chip
        label="Created by me"
        sx={{ mb: 1, mr: 1 }}
        color={changeViewState.createdBy ? 'primary' : 'default'}
        onClick={() => {
          const newState = { ...changeViewState };
          if (newState.createdBy) {
            newState.createdBy = undefined;
          } else {
            newState.createdBy = me;
          }
          dispatch(setChangeViewState(newState));
          resetPageNumber();
        }}
      />
      <Chip
        label="Owned by me"
        sx={{ mb: 1, mr: 1 }}
        color={changeViewState.owner ? 'primary' : 'default'}
        onClick={() => {
          const newState = { ...changeViewState };
          if (newState.owner) {
            newState.owner = undefined;
          } else {
            newState.owner = me;
          }
          dispatch(setChangeViewState(newState));
          resetPageNumber();
        }}
      />
      {Object.values(ChangeCandidateGroup).map((g) => (
        <Chip
          sx={{ mb: 1, mr: 1 }}
          label={ChangeCandidateGroupsDisplayMap[g]}
          color={changeViewState.candidateGroups?.find((c) => c === g) ? 'primary' : 'default'}
          onClick={() => {
            const newState = { ...changeViewState };
            if (!!newState.candidateGroups && !!newState.candidateGroups.find((c) => c === g)) {
              newState.candidateGroups = newState.candidateGroups.filter((c) => c !== g);
            } else {
              const newGroups = newState.candidateGroups || [];
              newState.candidateGroups = newGroups.concat(g);
            }
            dispatch(setChangeViewState(newState));
            resetPageNumber();
          }}
        />
      ))}
      <Tooltip title="View options">
        <IconButton size="small" sx={{ p: 0.5, mb: 1, mr: 1 }} onClick={handleFineTuneClick}>
          <TuneIcon fontSize="small" />
        </IconButton>
      </Tooltip>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        slotProps={{
          paper: {
            sx: {
              px: 2,
              py: 1,
              maxWidth: 300,
            },
          },
        }}
      >
        <Stack>
          <Typography variant="subtitle1">Columns</Typography>
          <Stack>
            {columnsOrder.map((setting, index) => (
              <div
                key={setting.column}
                draggable
                onDragStart={() => handleDragStart(index)}
                onDragOver={handleDragOver}
                onDrop={() => handleDrop(index)}
                style={{ display: 'flex', alignItems: 'center', cursor: 'grab' }}
              >
                <DragIndicatorIcon fontSize="small" style={{ marginRight: 8 }} />
                <FormControlLabel
                  control={<Checkbox checked={!setting.hidden} onChange={() => handleToggleColumn(setting.column)} />}
                  label={ChangeColumnDisplayMap[setting.column]}
                />
              </div>
            ))}
          </Stack>
        </Stack>
      </Popover>
    </Stack>
  );
}

export default ChangeChipFilter;
