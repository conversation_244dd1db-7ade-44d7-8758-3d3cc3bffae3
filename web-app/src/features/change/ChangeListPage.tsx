import { Tab<PERSON>ontext, TabPanel } from '@mui/lab';
import { Box, Paper, Tab, Tabs } from '@mui/material';
import { DataGrid, GridColDef, GridPaginationModel, GridRenderCellParams, GridSortModel } from '@mui/x-data-grid';
import AddIcon from '@mui/icons-material/Add';
import { useEffect, useMemo, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { useGetChangesQuery } from './changeApi';
import {
  ChangeColumn,
  ChangeColumnDefaults,
  ChangeColumnDisplayMap,
  ChangeFieldSortMap,
  ChangeParams,
  ChangeRead,
  ChangeSort,
  ChangeSortField,
  ChangeStatus,
  ChangeStatusDisplayMap,
} from './changeTypes';
import ResponsiveButton from '../../components/ResponsiveButton';
import ErrorGate from '../../components/ErrorGate';
import PageTitle from '../title/Title';
import ChangeFilterBar from './ChangeFilterBar';
import { PermissionType } from '../guard/guardTypes';
import { GuardResult } from '../guard/guardHooks';
import { UserRole, UserDisplay } from '../user/userTypes';
import Guard from '../guard/Guard';
import ChangeChipFilter from './ChangeChipFilter';
import { useGetCurrentUserQuery } from '../user/userApi';
import { useAppDispatch, useAppSelector } from '../../store';
import { setChangeViewState } from './changeSlice';
import { DataGridCellLinkWrapper } from '../../components/DataGridCellLink';
import GroupCell from '../../components/GroupCell';
import SidCell from '../../components/SidCell';
import UserCell from '../../components/UserCell';
import ChangeNameCell from './cell/ChangeNameCell';
import ChangeStatusCell from './cell/ChangeStatusCell';
import ChangeDateCell from './cell/ChangeDateCell';
import ChangeIssuesCell from './cell/ChangeIssuesCell';
import ChangeChecklistsCell from './cell/ChangeChecklistsCell';
import { GroupDisplay } from '../group/groupTypes';
import usePaging from '../../components/hooks/usePaging';
import NoRowsOverlay from '../../components/NoRowsOverlay';

const getChangeUrl = (changeId: number) => `${changeId}`;

const getGridModelFromSort = (sort: ChangeSort[]): GridSortModel =>
  sort.map((s) => ({
    field:
      Object.keys(ChangeFieldSortMap).find((key) => ChangeFieldSortMap[key as keyof ChangeRead] === s.field) || s.field,
    sort: s.direction,
  }));

const getSortFromGridModel = (sortModel: GridSortModel): ChangeSort[] =>
  sortModel.map((s) => ({
    field: (ChangeFieldSortMap[s.field as keyof ChangeRead] || s.field) as ChangeSortField,
    direction: s.sort as 'asc' | 'desc',
  }));

const columnDefaults: Record<ChangeColumn, GridColDef<ChangeRead>> = {
  [ChangeColumn.SID]: {
    field: ChangeColumn.SID,
    headerName: ChangeColumnDisplayMap[ChangeColumn.SID],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChangeRead, number, string>) =>
      DataGridCellLinkWrapper(SidCell(params), getChangeUrl(params.row.id)),
  },
  [ChangeColumn.TITLE]: {
    field: ChangeColumn.TITLE,
    headerName: ChangeColumnDisplayMap[ChangeColumn.TITLE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChangeRead, string, string>) =>
      DataGridCellLinkWrapper(ChangeNameCell(params), getChangeUrl(params.row.id)),
  },
  [ChangeColumn.GROUP]: {
    field: ChangeColumn.GROUP,
    headerName: ChangeColumnDisplayMap[ChangeColumn.GROUP],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChangeRead, string, string>) =>
      DataGridCellLinkWrapper(GroupCell(params), getChangeUrl(params.row.id)),
    valueGetter: (value: GroupDisplay) => (value ? value.name : ''),
  },
  [ChangeColumn.OWNER]: {
    field: ChangeColumn.OWNER,
    headerName: ChangeColumnDisplayMap[ChangeColumn.OWNER],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChangeRead, string, string>) =>
      DataGridCellLinkWrapper(UserCell(params), getChangeUrl(params.row.id)),
    valueGetter: (value: UserDisplay) => (value ? value.fullName : ''),
  },
  [ChangeColumn.ISSUES]: {
    field: ChangeColumn.ISSUES,
    headerName: ChangeColumnDisplayMap[ChangeColumn.ISSUES],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChangeRead>) =>
      DataGridCellLinkWrapper(ChangeIssuesCell(params), getChangeUrl(params.row.id)),
    valueGetter: (_, row) => `${row.resolvedIssuesCount} / ${row.issuesTotalCount}`,
  },
  [ChangeColumn.CHECKLISTS]: {
    field: ChangeColumn.CHECKLISTS,
    headerName: ChangeColumnDisplayMap[ChangeColumn.CHECKLISTS],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChangeRead>) =>
      DataGridCellLinkWrapper(ChangeChecklistsCell(params), getChangeUrl(params.row.id)),
    valueGetter: (_, row) => `${row.checklistsResolvedCount} / ${row.checklistsTotalCount}`,
  },
  [ChangeColumn.STATUS]: {
    field: ChangeColumn.STATUS,
    headerName: ChangeColumnDisplayMap[ChangeColumn.STATUS],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChangeRead, string, string>) =>
      DataGridCellLinkWrapper(ChangeStatusCell(params), getChangeUrl(params.row.id)),
    valueGetter: (value: ChangeStatus) => (value ? ChangeStatusDisplayMap[value] : ''),
  },
  [ChangeColumn.START_DATE]: {
    field: ChangeColumn.START_DATE,
    headerName: ChangeColumnDisplayMap[ChangeColumn.START_DATE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ChangeRead, number, string>) =>
      DataGridCellLinkWrapper(ChangeDateCell(params), getChangeUrl(params.row.id)),
    valueFormatter: (value: number) => (value ? new Date(value).toDateString() : ''),
  },
};

function ChangeListPage() {
  const { groupId } = useParams();
  const { page, setPage, pageSize, setPageSize } = usePaging();

  const { data: me } = useGetCurrentUserQuery();
  const changeViewState = useAppSelector((state) => state.change.changeViewState);
  const [sortModel, setSortModel] = useState<GridSortModel>(getGridModelFromSort(changeViewState?.sort || []));
  const dispatch = useAppDispatch();

  const columns = useMemo(() => {
    const cols = changeViewState.columns ? changeViewState.columns : ChangeColumnDefaults;
    return cols
      .filter((c) => !c.hidden)
      .map((c) => ({
        ...columnDefaults[c.column],
        width: c.width,
      }));
  }, [changeViewState.columns]);

  const getFilter = (view?: 'open' | 'all') => {
    const usedView = view || changeViewState.listView;
    if (usedView === 'all') {
      return `ancestorId=${groupId}`;
    }

    return `
      (status=${ChangeStatus.REQUESTED}|status=${ChangeStatus.ACCEPTED}|status=${ChangeStatus.IMPLEMENTED}|status=${ChangeStatus.IMPLEMENTING})
      &
      ancestorId=${groupId}
    `
      .replace(/\n/g, '')
      .replace(/\s/g, '');
  };

  const [queryParams, setQueryParams] = useState<ChangeParams>({
    pageSize,
    pageNumber: page,
    groupId: changeViewState.group?.id,
    status: changeViewState.status,
    createdBy: changeViewState.createdBy?.id,
    search: changeViewState.search,
    candidateGroups: changeViewState.candidateGroups,
    filter: getFilter(),
  });
  const { data, isLoading, error } = useGetChangesQuery(queryParams);

  const canRequestChange = (guardResult: GuardResult) =>
    guardResult.hasRole(UserRole.TENANT_ADMIN) || guardResult.hasPermission(PermissionType.CHANGE_CREATE);

  useEffect(() => {
    setQueryParams((prev) => ({
      ...prev,
      pageSize,
      pageNumber: page,
    }));
  }, [page, pageSize]);

  useEffect(() => {
    if (changeViewState) {
      const sort =
        changeViewState.sort && changeViewState.sort.length > 0
          ? changeViewState.sort.map((s) => `${s.field}:${s.direction}`).join(',')
          : undefined;

      setQueryParams((prev) => ({
        ...prev,
        groupId: changeViewState.group?.id,
        createdBy: changeViewState.createdBy?.id,
        status: changeViewState.status,
        search: changeViewState.search,
        candidateGroups: changeViewState.candidateGroups,
        owner: changeViewState.owner?.id,
        sort,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [changeViewState]);

  useEffect(() => {
    setQueryParams((prev) => ({
      ...prev,
      filter: getFilter(changeViewState.listView),
    }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [changeViewState.listView]);

  const onTabSwitch = (view: 'open' | 'all') => {
    setPage(0);
    dispatch(
      setChangeViewState({
        ...changeViewState,
        listView: view,
      })
    );
  };

  const handlePaginationChange = (updatedModel: GridPaginationModel) => {
    setPageSize(updatedModel.pageSize);
    setPage(updatedModel.page);
  };

  const handleSortModelChange = (newSortModel: GridSortModel) => {
    setSortModel(newSortModel);
    const newSort = getSortFromGridModel(newSortModel);
    dispatch(
      setChangeViewState({
        ...changeViewState,
        sort: newSort,
      })
    );
  };

  const resetPageNumber = (): void => {
    handlePaginationChange({ page: 0, pageSize });
  };

  return (
    <ErrorGate error={error}>
      <PageTitle page="Changes" />
      <TabContext value="0">
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Tabs value={changeViewState.listView} sx={{ width: 'fit-content' }}>
            <Tab label="Open" value="open" onClick={() => onTabSwitch('open')} />
            <Tab label="All" value="all" onClick={() => onTabSwitch('all')} />
          </Tabs>
          <Guard hasAccess={canRequestChange}>
            <Box>
              <ResponsiveButton component={Link} to="add" variant="contained" size="large" endIcon={<AddIcon />}>
                Request change
              </ResponsiveButton>
            </Box>
          </Guard>
        </Box>
        <ChangeChipFilter me={me} resetPageNumber={resetPageNumber} />
        <ChangeFilterBar groupId={Number(groupId)} resetPageNumber={resetPageNumber} />
        <TabPanel
          sx={{
            px: 0,
            pt: 1,
            pb: 0,
          }}
          value="0"
        >
          <Paper elevation={4}>
            <Box
              sx={{
                height: 'calc(100vh - 269px)',
                overflow: 'hidden',
                '@media (max-height: 600px)': {
                  height: '100%',
                },
              }}
            >
              <DataGrid
                rows={data?.content || []}
                columns={columns}
                rowCount={data?.total || 0}
                loading={isLoading}
                disableColumnMenu
                pagination
                paginationMode="server"
                paginationModel={{ page, pageSize }}
                onPaginationModelChange={handlePaginationChange}
                sortingMode="server"
                sortModel={sortModel}
                onSortModelChange={handleSortModelChange}
                disableRowSelectionOnClick
                slots={{
                  noRowsOverlay: NoRowsOverlay,
                }}
                onColumnWidthChange={(params) => {
                  const newViewState = { ...changeViewState };
                  if (newViewState.columns) {
                    // Clone the columns array to avoid direct mutation.
                    const updatedColumns = [...newViewState.columns];
                    // Find the column to update.
                    const columnToUpdate = updatedColumns.find((c) => c.column === params.colDef.field);
                    if (columnToUpdate) {
                      // Get the index of the column and update immutably.
                      const index = updatedColumns.indexOf(columnToUpdate);
                      updatedColumns[index] = { ...columnToUpdate, width: params.width };
                    }
                    newViewState.columns = updatedColumns;
                  }
                  dispatch(setChangeViewState(newViewState));
                }}
                slotProps={{
                  loadingOverlay: { variant: 'skeleton', noRowsVariant: 'skeleton' },
                  noRowsOverlay: { title: 'No changes found' },
                }}
              />
            </Box>
          </Paper>
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}

export default ChangeListPage;
