import { GridRenderCellParams } from '@mui/x-data-grid';
import ChecklistIcon from '@mui/icons-material/ChecklistOutlined';
import { ChangeRead } from '../changeTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

type ChangeChecklistsCellParam = GridRenderCellParams<ChangeRead>;

export default function ChangeChecklistsCell(params: ChangeChecklistsCellParam) {
  const { row } = params;
  const { checklistsResolvedCount, checklistsTotalCount } = row;

  const displayText = `${checklistsResolvedCount} / ${checklistsTotalCount}`;

  return (
    <Cell title={displayText}>
      <ChecklistIcon fontSize="small" /> <CellText>{displayText}</CellText>
    </Cell>
  );
}
