import { GridRenderCellParams } from '@mui/x-data-grid';
import { ChangeRead } from '../changeTypes';
import CellText from '../../../components/CellText';
import Cell from '../../../components/Cell';

type ChangeNameCellParam = GridRenderCellParams<ChangeRead, string>;

export default function ChangeNameCell(params: ChangeNameCellParam) {
  const { formattedValue: title } = params;

  if (!title || title.length === 0) {
    return null;
  }

  return (
    <Cell title={title}>
      <CellText>{title}</CellText>
    </Cell>
  );
}
