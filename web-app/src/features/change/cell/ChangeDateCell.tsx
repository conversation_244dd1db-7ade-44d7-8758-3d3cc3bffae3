import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import { ChangeRead } from '../changeTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

type ChangeDateCellParam = GridRenderCellParams<ChangeRead, Date | number, string>;

export default function ChangeDateCell(params: ChangeDateCellParam) {
  const { formattedValue: formattedDate } = params;

  if (!formattedDate) {
    return null;
  }

  return (
    <Cell title={formattedDate}>
      <DateRangeIcon fontSize="small" /> <CellText>{formattedDate}</CellText>
    </Cell>
  );
}
