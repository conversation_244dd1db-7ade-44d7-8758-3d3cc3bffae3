import { GridRenderCellParams } from '@mui/x-data-grid';
import ErrorIcon from '@mui/icons-material/Error';
import { ChangeRead } from '../changeTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

type ChangeIssuesCellParam = GridRenderCellParams<ChangeRead>;

export default function ChangeIssuesCell(params: ChangeIssuesCellParam) {
  const { row } = params;
  const { resolvedIssuesCount, issuesTotalCount } = row;

  const displayText = `${resolvedIssuesCount} / ${issuesTotalCount}`;

  return (
    <Cell title={displayText}>
      <ErrorIcon color="error" fontSize="small" sx={{ opacity: 1 }} /> <CellText>{displayText}</CellText>
    </Cell>
  );
}
