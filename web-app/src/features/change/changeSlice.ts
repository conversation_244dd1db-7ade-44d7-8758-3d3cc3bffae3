import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { ChangeColumnDefaults, ChangeState, ChangeViewState } from './changeTypes';

const initialState: ChangeState = {
  changeViewState: {
    listView: 'open',
    columns: ChangeColumnDefaults,
  },
};

export const changeSlice = createSlice({
  name: 'change',
  initialState,
  reducers: {
    setChangeViewState: (state, action: PayloadAction<ChangeViewState>) => {
      state.changeViewState = action.payload;
    },
  },
});

export const { setChangeViewState } = changeSlice.actions;

export const changeReducer = persistReducer(
  {
    key: 'change',
    storage,
    whitelist: ['changeViewState'],
  },
  changeSlice.reducer
);
