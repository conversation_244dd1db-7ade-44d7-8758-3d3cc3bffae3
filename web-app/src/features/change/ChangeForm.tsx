import { <PERSON><PERSON>, Text<PERSON>ield, Box, Typography, Tooltip, IconButton } from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { DatePicker } from '@mui/x-date-pickers';
import AddIcon from '@mui/icons-material/Add';
import { useEffect, useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Link } from 'react-router-dom';
import { ChangeFormInput } from './changeTypes';
import GroupField from '../group/GroupField';
import { isValidDayjs, preventSubmitOnEnter } from '../../utils';
import ChecklistList from '../checklist/ChecklistList';
import FileUpload from '../file/FileUpload';
import UnsavedChangesDialog from '../../components/UnsavedChangesDialog';
import UserSelect from '../user/UserSelect';

dayjs.Ls.en.weekStart = 1;

const schema = yup.object({
  group: yup.object().nullable().required().label('Group'),
  owner: yup.object().nullable().label('Owner'),
  title: yup.string().required().label('Title'),
  description: yup.string().required().label('Description'),
  scope: yup.string().required().label('Scope'),
  purpose: yup.string().required().label('Purpose'),
  participants: yup.string().required().label('Participants'),
  startDate: yup.mixed<Dayjs>().nullable().required().test(isValidDayjs).label('Start date'),
  endDate: yup.mixed<Dayjs>().nullable().test(isValidDayjs).label('End date'),
  files: yup.array().required().label('Documents'),
});

const defaultSchema: ChangeFormInput = {
  group: null,
  title: '',
  description: '',
  scope: '',
  purpose: '',
  participants: '',
  owner: null,
  startDate: null,
  endDate: null,
  files: [],
};

interface ChangeFormProps {
  onDirtyChange?: (isDirty: boolean) => void;
  defaultValues?: Partial<ChangeFormInput>;
  onSubmit: (form: ChangeFormInput) => void;
  isEditing?: boolean;
  submitError: boolean;
  locked?: boolean;
  canEdit?: boolean;
  canViewChecklists?: boolean;
  submitButton?: React.ReactElement;
}

function ChangeForm({
  onDirtyChange,
  defaultValues,
  onSubmit,
  submitError,
  submitButton,
  isEditing,
  locked = false,
  canEdit = true,
  canViewChecklists = false,
}: ChangeFormProps) {
  const readOnly = locked || !canEdit;

  const [assignedGroup, setAssignedGroup] = useState<number | null>(null);

  const {
    handleSubmit,
    control,
    reset,
    formState: { isDirty },
  } = useForm<ChangeFormInput>({
    resolver: yupResolver(schema),
    defaultValues: { ...defaultSchema, ...defaultValues },
  });

  useEffect(() => {
    if (onDirtyChange) {
      onDirtyChange(isDirty);
    }
  }, [isDirty, onDirtyChange]);

  useEffect(() => {
    // reset form if new default values are present
    reset({ ...defaultSchema, ...defaultValues });
  }, [defaultValues, reset]);

  return (
    <form id="change-form" role="presentation" onSubmit={handleSubmit(onSubmit)} onKeyDown={preventSubmitOnEnter}>
      <Typography variant="h6">Change request</Typography>
      {!isEditing && (
        <Controller
          name="group"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <GroupField
              label="Group"
              onGroupChange={(e) => {
                field.onChange(e);
                if (e?.id !== null && e?.id !== undefined) {
                  setAssignedGroup(e?.id);
                }
              }}
              errorMessage={error?.message}
            />
          )}
        />
      )}
      <Controller
        name="title"
        control={control}
        render={({ field, fieldState: { error } }) => (
          <TextField
            name={field.name}
            ref={field.ref}
            onChange={field.onChange}
            onBlur={field.onBlur}
            value={field.value}
            label="Title"
            size="small"
            fullWidth
            autoComplete="off"
            margin="normal"
            error={!!error}
            helperText={error?.message}
            slotProps={{
              htmlInput: {
                readOnly,
                maxLength: 255,
              },

              inputLabel: {
                shrink: isEditing,
              },
            }}
          />
        )}
      />
      <Controller
        name="description"
        control={control}
        render={({ field, fieldState: { error } }) => (
          <TextField
            name={field.name}
            ref={field.ref}
            onChange={field.onChange}
            onBlur={field.onBlur}
            value={field.value}
            label="Description"
            size="small"
            fullWidth
            multiline
            minRows={2}
            autoComplete="off"
            margin="normal"
            error={!!error}
            helperText={error?.message}
            slotProps={{
              htmlInput: {
                readOnly,
                maxLength: 4080,
              },

              inputLabel: {
                shrink: isEditing,
              },
            }}
          />
        )}
      />
      <Controller
        name="purpose"
        control={control}
        render={({ field, fieldState: { error } }) => (
          <TextField
            name={field.name}
            ref={field.ref}
            onChange={field.onChange}
            onBlur={field.onBlur}
            value={field.value}
            label="Purpose"
            size="small"
            fullWidth
            multiline
            minRows={2}
            autoComplete="off"
            margin="normal"
            error={!!error}
            helperText={error?.message}
            slotProps={{
              htmlInput: {
                readOnly,
                maxLength: 4080,
              },

              inputLabel: {
                shrink: isEditing,
              },
            }}
          />
        )}
      />
      <Controller
        name="scope"
        control={control}
        render={({ field, fieldState: { error } }) => (
          <TextField
            name={field.name}
            ref={field.ref}
            onChange={field.onChange}
            onBlur={field.onBlur}
            value={field.value}
            label="Scope"
            size="small"
            fullWidth
            multiline
            minRows={2}
            autoComplete="off"
            margin="normal"
            error={!!error}
            helperText={error?.message}
            slotProps={{
              htmlInput: {
                readOnly,
                maxLength: 4080,
              },

              inputLabel: {
                shrink: isEditing,
              },
            }}
          />
        )}
      />
      {isEditing && (
        <Controller
          name="owner"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <UserSelect
              label="Owner"
              field={field}
              assignedGroup={assignedGroup}
              error={error}
              margin="normal"
              readOnly={readOnly}
            />
          )}
        />
      )}
      <Controller
        name="participants"
        control={control}
        render={({ field, fieldState: { error } }) => (
          <TextField
            name={field.name}
            ref={field.ref}
            onChange={field.onChange}
            onBlur={field.onBlur}
            value={field.value}
            label="Participants"
            size="small"
            fullWidth
            multiline
            autoComplete="off"
            margin="normal"
            error={!!error}
            helperText={error?.message}
            slotProps={{
              htmlInput: {
                readOnly,
                maxLength: 255,
              },

              inputLabel: {
                shrink: isEditing,
              },
            }}
          />
        )}
      />
      <Box display="flex" flex="1" gap={{ xs: 0, sm: 2 }} flexWrap={{ xs: 'wrap', sm: 'unset' }}>
        <Controller
          name="startDate"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <DatePicker
              label="Start date"
              displayWeekNumber
              disablePast
              format="DD/MM/YYYY"
              value={field.value}
              onChange={(newValue) => {
                field.onChange(newValue ? newValue.hour(0).minute(0).second(0).millisecond(0) : newValue);
              }}
              readOnly={readOnly}
              slotProps={{
                textField: {
                  fullWidth: true,
                  autoComplete: 'off',
                  margin: 'normal',
                  size: 'small',
                  InputLabelProps: { shrink: isEditing },
                  error: !!error,
                  helperText: error?.message,
                },
              }}
            />
          )}
        />
        <Controller
          name="endDate"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <DatePicker
              label="End date (optional)"
              displayWeekNumber
              disablePast
              format="DD/MM/YYYY"
              value={field.value}
              onChange={(newValue) => {
                field.onChange(newValue ? newValue.hour(0).minute(0).second(0).millisecond(0) : newValue);
              }}
              readOnly={readOnly}
              slotProps={{
                field: {
                  clearable: true,
                },
                textField: {
                  fullWidth: true,
                  autoComplete: 'off',
                  margin: 'normal',
                  size: 'small',
                  InputLabelProps: { shrink: isEditing },
                  error: !!error,
                  helperText: error?.message,
                },
              }}
            />
          )}
        />
      </Box>
      <Box mb={1}>
        <Controller
          name="files"
          control={control}
          render={({ field }) => (
            <FileUpload label="Documents" readOnly={readOnly} onChange={field.onChange} value={field.value} />
          )}
        />
      </Box>
      {isEditing && canViewChecklists && (
        <>
          <Box display="flex">
            <Typography mt={1} mb={1} variant="h6">
              Checklists
            </Typography>
            {!readOnly && (
              <Tooltip title="Add checklist" arrow>
                <IconButton
                  component={Link}
                  size="medium"
                  to="./checklists/add"
                  sx={{ height: 'min-content', alignSelf: 'center' }}
                >
                  <AddIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>
          <ChecklistList />
        </>
      )}
      <UnsavedChangesDialog isDirty={isDirty} />
      {submitError && (
        <Alert severity="error" sx={{ mt: 1 }}>
          Something went wrong on our servers
        </Alert>
      )}
      <Box display="flex" justifyContent="flex-end" mt={3}>
        {submitButton}
      </Box>
    </form>
  );
}
export default ChangeForm;
