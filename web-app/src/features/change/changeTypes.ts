import { Dayjs } from 'dayjs';
import { GroupDisplay, GroupListRead } from '../group/groupTypes';
import { User, UserDisplay } from '../user/userTypes';
import { FileDisplay } from '../file/fileTypes';
import { ChecklistTemplateDisplay } from '../checklist/template/checklistTemplateTypes';
import { themeToColor } from '../../theme';
import { EnumMetaItem, EnumMetaMap } from '../../types';
import { createDynamicMetaMap, getWeekNumberAndYear, sortDates } from '../../utils';

export enum ChangeStatus {
  REQUESTED = 'REQUESTED',
  ACCEPTED = 'ACCEPTED',
  IMPLEMENTING = 'IMPLEMENTING',
  IMPLEMENTED = 'IMPLEMENTED',
  CLOSED = 'CLOSED',
  REJECTED = 'REJECTED',
  CANCELED = 'CANCELED',
}

export interface ChangePDFParams {
  id: number;
  timeZone: string;
}

export const ChangeStatusDisplayMap: Record<ChangeStatus, string> = {
  REQUESTED: 'Requested',
  ACCEPTED: 'Accepted',
  IMPLEMENTING: 'Implementing',
  IMPLEMENTED: 'Implemented',
  CLOSED: 'Closed',
  REJECTED: 'Rejected',
  CANCELED: 'Canceled',
};
export interface ChangeParams {
  groupId?: number;
  ancestorId?: number;
  createdBy?: number;
  owner?: number;
  search?: string;
  status?: ChangeStatus;
  statusNot?: ChangeStatus;
  candidateGroups?: ChangeCandidateGroup[];
  filter?: string;
  sort?: string;
  pageNumber?: number;
  pageSize?: number;
}

export interface ChangeRead {
  id: number;
  sid: number;
  title: string;
  description: string;
  scope: string;
  purpose: string;
  participants: string;
  startDate: number;
  endDate?: number;
  group: GroupDisplay;
  processInstanceId: string;
  createdBy: UserDisplay;
  owner: UserDisplay;
  resolvedIssuesCount: number;
  issuesTotalCount: number;
  checklistsTotalCount: number;
  checklistsResolvedCount: number;
  locked: boolean;
  status: ChangeStatus;
  files: FileDisplay[];
}

export interface ChangeChecklist {
  id: number;
  sid: number;
  checklistTemplate: ChecklistTemplateDisplay;
}

export interface ChangeHistoryRead extends ChangeRead {
  checklists: ChangeChecklist[];
}

export interface ChangeDisplay {
  id: number;
  sid: number;
  title: string;
}
export interface ChangeCreate {
  title: string;
  description: string;
  scope: string;
  purpose: string;
  participants: string;
  startDate: number;
  endDate?: number;
  group: number;
  files: number[];
  owner?: number;
}

export interface ChangeUpdate {
  id: number;
  title: string;
  description: string;
  scope: string;
  purpose: string;
  participants: string;
  startDate: number;
  endDate?: number;
  files: number[];
  owner?: number;
}

export interface ChangeFormInput {
  title: string;
  description: string;
  scope: string;
  purpose: string;
  participants: string;
  startDate: Dayjs | null;
  endDate?: Dayjs | null;
  group: GroupDisplay | null;
  files: FileDisplay[];
  owner?: UserDisplay | null;
}

export interface ChangeViewState {
  listView: 'open' | 'all';
  group?: GroupListRead;
  createdBy?: User;
  owner?: User;
  status?: ChangeStatus;
  candidateGroups?: ChangeCandidateGroup[];
  search?: string;
  sort?: ChangeSort[];
  columns?: ChangeColumnSetting[];
}

export interface ChangeState {
  changeViewState: ChangeViewState;
}

export enum ChangeCandidateGroup {
  CHANGE_ACCEPT = 'CHANGE_ACCEPT',
  CHANGE_REVIEW = 'CHANGE_REVIEW',
  CHANGE_IMPLEMENT = 'CHANGE_IMPLEMENT',
  CHANGE_CHECK = 'CHANGE_CHECK',
  CHANGE_CLOSE = 'CHANGE_CLOSE',
}

export const ChangeCandidateGroupsDisplayMap: Record<ChangeCandidateGroup, string> = {
  CHANGE_ACCEPT: 'Accept',
  CHANGE_REVIEW: 'Review',
  CHANGE_IMPLEMENT: 'Implement',
  CHANGE_CHECK: 'Check',
  CHANGE_CLOSE: 'Close',
};

export interface ChangeChange {
  by: UserDisplay;
  at: number;
  type: ChangeChangeType;
  oldEntity: ChangeRead;
  newEntity: ChangeRead;
}

export enum ChangeChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export const ChangeChangeTypeDisplayMap: Record<ChangeChangeType, string> = {
  INSERT: 'Change requested',
  UPDATE: 'Change updated',
  DELETE: 'Change deleted',
};

export enum ChangeGroupBy {
  GROUP = 'CHANGES_GROUP',
  START_DATE_WEEK = 'CHANGES_START_DATE_WEEK',
}

export const ChangeGroupByDisplayMap: Record<ChangeGroupBy, string> = {
  [ChangeGroupBy.GROUP]: 'Group',
  [ChangeGroupBy.START_DATE_WEEK]: 'Change week',
};

export enum ChangeSortField {
  SID = 'sid',
  TITLE = 'title',
  START_DATE = 'start_date',
  STATUS = 'status',
}

export const ChangeFieldSortMap: Partial<Record<keyof ChangeRead, ChangeSortField>> = {
  sid: ChangeSortField.SID,
  title: ChangeSortField.TITLE,
  startDate: ChangeSortField.START_DATE,
  status: ChangeSortField.STATUS,
};

export interface ChangeSort {
  field: ChangeSortField;
  direction: 'asc' | 'desc';
}

export enum ChangeColumn {
  SID = 'sid',
  TITLE = 'title',
  GROUP = 'group',
  OWNER = 'owner',
  ISSUES = 'issues',
  CHECKLISTS = 'checklists',
  STATUS = 'status',
  START_DATE = 'startDate',
}

export const ChangeColumnDisplayMap: Record<ChangeColumn, string> = {
  [ChangeColumn.SID]: 'ID',
  [ChangeColumn.TITLE]: 'Title',
  [ChangeColumn.GROUP]: 'Group',
  [ChangeColumn.OWNER]: 'Owner',
  [ChangeColumn.ISSUES]: 'Issues',
  [ChangeColumn.CHECKLISTS]: 'Checklists',
  [ChangeColumn.STATUS]: 'Status',
  [ChangeColumn.START_DATE]: 'Start date',
};

export interface ChangeColumnSetting {
  column: ChangeColumn;
  hidden: boolean;
  width: number;
}

export const ChangeColumnDefaults: ChangeColumnSetting[] = [
  {
    column: ChangeColumn.SID,
    hidden: false,
    width: 75,
  },
  {
    column: ChangeColumn.TITLE,
    hidden: false,
    width: 400,
  },
  {
    column: ChangeColumn.GROUP,
    hidden: false,
    width: 200,
  },
  {
    column: ChangeColumn.OWNER,
    hidden: false,
    width: 200,
  },
  {
    column: ChangeColumn.ISSUES,
    hidden: false,
    width: 100,
  },
  {
    column: ChangeColumn.CHECKLISTS,
    hidden: false,
    width: 100,
  },
  {
    column: ChangeColumn.STATUS,
    hidden: false,
    width: 135,
  },
  {
    column: ChangeColumn.START_DATE,
    hidden: false,
    width: 175,
  },
];

export const CreationDateWeekMeta = createDynamicMetaMap<string, EnumMetaItem>((iso) => ({
  label: getWeekNumberAndYear(iso),
  color: themeToColor('primary.main'),
}));

export interface ChangeGroupByFieldType {
  [ChangeGroupBy.START_DATE_WEEK]: string;
}

export const ChangeGroupByFieldMetaMap: {
  [K in keyof ChangeGroupByFieldType]: EnumMetaMap<ChangeGroupByFieldType[K]>;
} = {
  [ChangeGroupBy.START_DATE_WEEK]: CreationDateWeekMeta,
};

export const ChangeGroupByFieldSortFunctionMap = {
  [ChangeGroupBy.START_DATE_WEEK]: sortDates,
};
