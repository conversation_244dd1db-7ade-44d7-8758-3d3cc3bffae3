import dayjs from 'dayjs';
import {
  CountObject,
  hasSortFunction,
  KeyTotal,
  StatsGroupBy,
  StatsGroupByFieldSortFunctionMap,
  StatsKeyValue,
  TimeSeriesDataPoint,
  WeekTotalCount,
} from './statsTypes';

export function isCountObject(value: StatsKeyValue | CountObject): value is CountObject {
  return value !== null && typeof value === 'object' && 'count' in value;
}

export function isStatsKeyValue(value: StatsKeyValue | CountObject): value is StatsKeyValue {
  if (!value || typeof value !== 'object') {
    return false;
  }

  return Object.values(value).every((v) => isCountObject(v));
}

export function statsToTimeSeries(data: StatsKeyValue): TimeSeriesDataPoint[] {
  return Object.entries(data).flatMap(([date, value]): TimeSeriesDataPoint[] => {
    if (isCountObject(value)) {
      return [{ date, total: (value as CountObject).count }];
    }

    const nested = value as StatsKeyValue;
    const keyTotals: KeyTotal[] = Object.entries(nested)
      .filter(([, v]) => isCountObject(v))
      .map(([key, v]) => ({
        key,
        total: (v as CountObject).count,
      }));

    return [{ date, total: keyTotals }];
  });
}

export function generateCompleteTimeSeries(
  newData: TimeSeriesDataPoint[],
  startDate: number,
  endDate: number
): WeekTotalCount[] {
  const dataMap: Record<string, TimeSeriesDataPoint> = {};

  newData.forEach((item) => {
    const dateObj = dayjs(item.date);
    const week = dateObj.isoWeek();
    const year = dateObj.year();
    dataMap[`${year}-${week}`] = item;
  });

  const start = dayjs(startDate);
  const end = dayjs(endDate);
  const completeSeries: WeekTotalCount[] = [];

  let current = start.startOf('isoWeek');
  while (current.isBefore(end) || current.isSame(end, 'day')) {
    const year = current.year();
    const week = current.isoWeek();
    const date = `${year}-${week}`;

    completeSeries.push({
      week,
      year,
      total: dataMap[date]?.total || 0,
    });

    current = current.add(1, 'week');
  }

  return completeSeries;
}

export function sortByGroupKeys(statsData: StatsKeyValue, groupBy?: StatsGroupBy): StatsKeyValue {
  const groupKeySortFn = groupBy && hasSortFunction(groupBy) && StatsGroupByFieldSortFunctionMap[groupBy];

  if (!groupKeySortFn) {
    return { ...statsData };
  }

  return Object.fromEntries(Object.entries(statsData).sort(([a], [b]) => groupKeySortFn(a, b)));
}
