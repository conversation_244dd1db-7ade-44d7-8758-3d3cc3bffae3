package com.vinkey.restapi.managementofchange.checklist;

import com.vinkey.restapi.common.persistence.sort.SortByEnum;

public enum ChecklistSortBy implements SortByEnum {
  ID(Checklist_.ID),
  SID(Checklist_.SID),
  STATUS(Checklist_.STATUS),
  CREATED_AT(Checklist_.CREATION_DATE),
  MODIFIED_AT(Checklist_.MODIFIED_DATE);

  private final String field;

  ChecklistSortBy(String field) {
    this.field = field;
  }

  @Override
  public String getField() {
    return field;
  }
}
