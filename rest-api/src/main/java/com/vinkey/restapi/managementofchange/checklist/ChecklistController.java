package com.vinkey.restapi.managementofchange.checklist;

import com.vinkey.restapi.common.file.FileService;
import com.vinkey.restapi.common.persistence.Deletable;
import com.vinkey.restapi.common.persistence.PaginatedResult;
import com.vinkey.restapi.identityandaccess.auth.dto.JwtDetails;
import com.vinkey.restapi.managementofchange.change.Change;
import com.vinkey.restapi.managementofchange.change.ChangeService;
import com.vinkey.restapi.managementofchange.checklist.dto.ChecklistChange;
import com.vinkey.restapi.managementofchange.checklist.dto.ChecklistCreate;
import com.vinkey.restapi.managementofchange.checklist.dto.ChecklistRead;
import com.vinkey.restapi.managementofchange.checklist.dto.ChecklistUpdate;
import com.vinkey.restapi.managementofchange.checklist.template.ChecklistTemplate;
import com.vinkey.restapi.managementofchange.checklist.template.ChecklistTemplateService;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/checklists")
public class ChecklistController {
  private final ChangeService changeService;
  private final ChecklistService checklistService;
  private final ChecklistTemplateService checklistTemplateService;
  private final ChecklistMapper checklistMapper;
  private final FileService fileService;
  private final ChecklistPdfService checklistPdfService;
  private final ChecklistHistoryService checklistHistoryService;

  public ChecklistController(
      ChangeService changeService,
      ChecklistService checklistService,
      ChecklistTemplateService checklistTemplateService,
      FileService fileService,
      ChecklistPdfService checklistPdfService,
      ChecklistHistoryService checklistHistoryService) {
    this.changeService = changeService;
    this.checklistService = checklistService;
    this.checklistTemplateService = checklistTemplateService;
    this.checklistMapper = ChecklistMapper.INSTANCE;
    this.fileService = fileService;
    this.checklistPdfService = checklistPdfService;
    this.checklistHistoryService = checklistHistoryService;
  }

  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public ChecklistRead createChecklist(
      @Valid @RequestBody ChecklistCreate checklistCreate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    Change change = changeService.read(checklistCreate.getChange(), jwtDetails.getTenantId());
    ChecklistTemplate template =
        checklistTemplateService.read(
            checklistCreate.getChecklistTemplate(), jwtDetails.getTenantId());
    Checklist referenceChecklist =
        checklistMapper.checklistCreateChecklist(
            checklistCreate, jwtDetails.getTenantId(), change.getGroup().getId());
    referenceChecklist.setChecklistTemplate(template);
    referenceChecklist.setChange(change);
    Checklist newChecklistEntity = checklistService.create(referenceChecklist);

    return checklistMapper.checklistToChecklistRead(newChecklistEntity);
  }

  @GetMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public ChecklistRead getChecklist(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Checklist checklist = checklistService.read(id, jwtDetails.getTenantId());
    checklist
        .getAnswers()
        .forEach(
            o -> {
              o.setFiles(
                  fileService.setUrls(o.getFiles().stream().toList()).stream()
                      .collect(Collectors.toSet()));
            });
    return checklistMapper.checklistToChecklistRead(checklist);
  }

  @GetMapping
  @ResponseStatus(HttpStatus.OK)
  public PaginatedResult<ChecklistRead> getChecklists(
      @RequestParam(required = false) Long tenantId,
      @RequestParam(required = false) Long groupId,
      @RequestParam(required = false) Long ancestorGroupId,
      @RequestParam(required = false) Long createdBy,
      @RequestParam(required = false) Long assignee,
      @RequestParam(required = false) ChecklistStatus status,
      @RequestParam(required = false) Long changeId,
      @RequestParam(required = false) String search,
      @RequestParam(required = false) String filter,
      @RequestParam(required = false) String sort,
      @RequestParam(required = false) Long pageNumber,
      @RequestParam(required = false) Long pageSize,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    Page<Checklist> checklists =
        checklistService.readAll(
            jwtDetails.getTenantId(),
            groupId,
            ancestorGroupId,
            createdBy,
            assignee,
            status,
            changeId,
            search,
            filter,
            sort,
            pageNumber,
            pageSize);
    return checklistMapper.checklistToChecklistRead(checklists);
  }

  @GetMapping("{id}/deletable")
  @ResponseStatus(HttpStatus.OK)
  public Deletable getChecklistDeletable(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Checklist checklist = checklistService.read(id, jwtDetails.getTenantId());

    Boolean deletable = checklistService.checkDeletable(checklist);

    Deletable checklistDeletable = new Deletable();
    checklistDeletable.setDeletable(deletable);
    return checklistDeletable;
  }

  @PutMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public ChecklistRead updateChecklistRead(
      @PathVariable Long id,
      @Valid @RequestBody ChecklistUpdate checklistUpdate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {

    Checklist checklist = checklistService.read(id, jwtDetails.getTenantId());
    checklistMapper.updateChecklistFromChecklistUpdate(checklistUpdate, checklist);
    Checklist updatedChecklist = checklistService.update(checklist);
    return checklistMapper.checklistToChecklistRead(updatedChecklist);
  }

  @DeleteMapping("/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteChecklist(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Checklist checklist = checklistService.read(id, jwtDetails.getTenantId());
    checklistService.delete(checklist);
  }

  @PostMapping("/{id}/cancel")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void cancelChecklist(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Checklist checklist = checklistService.read(id, jwtDetails.getTenantId());
    checklistService.cancel(checklist);
  }

  @GetMapping("/{id}/pdf")
  @ResponseBody
  public void getPdf(
      @PathVariable Long id,
      @RequestParam(required = true) String timeZone,
      @AuthenticationPrincipal JwtDetails jwtDetails,
      HttpServletResponse response)
      throws IOException {
    Checklist checklist = checklistService.read(id, jwtDetails.getTenantId());
    response.setContentType(MediaType.APPLICATION_PDF_VALUE);
    response.addHeader(
        HttpHeaders.CONTENT_DISPOSITION,
        "attachment; filename=Checklist " + checklist.getSid() + ".pdf");
    checklistPdfService.generatePdf(checklist, timeZone, response.getOutputStream());
  }

  @GetMapping("/{id}/history")
  @ResponseStatus(HttpStatus.OK)
  public List<ChecklistChange> getHistory(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    return checklistHistoryService.getHistory(id, jwtDetails.getTenantId());
  }
}
