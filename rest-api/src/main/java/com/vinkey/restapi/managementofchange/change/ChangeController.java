package com.vinkey.restapi.managementofchange.change;

import com.vinkey.restapi.common.file.FileService;
import com.vinkey.restapi.common.persistence.Deletable;
import com.vinkey.restapi.common.persistence.PaginatedResult;
import com.vinkey.restapi.identityandaccess.auth.dto.JwtDetails;
import com.vinkey.restapi.managementofchange.change.dto.ChangeChange;
import com.vinkey.restapi.managementofchange.change.dto.ChangeCreate;
import com.vinkey.restapi.managementofchange.change.dto.ChangeListRead;
import com.vinkey.restapi.managementofchange.change.dto.ChangeRead;
import com.vinkey.restapi.managementofchange.change.dto.ChangeUpdate;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/changes")
public class ChangeController {
  private final ChangeService changeService;
  private final ChangeHistoryService changeHistoryService;
  private final ChangeMapper changeMapper;
  private final FileService fileService;

  public ChangeController(
      ChangeService changeService,
      ChangeHistoryService changeHistoryService,
      FileService fileService) {
    this.changeService = changeService;
    this.changeHistoryService = changeHistoryService;
    this.changeMapper = ChangeMapper.INSTANCE;
    this.fileService = fileService;
  }

  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public ChangeRead createChange(
      @RequestBody ChangeCreate changeCreate, @AuthenticationPrincipal JwtDetails jwtDetails) {
    return changeMapper.changeToChangeRead(
        changeService.read(
            changeService.create(
                changeMapper.changeCreateToChange(changeCreate, jwtDetails.getTenantId())),
            jwtDetails.getTenantId()));
  }

  @GetMapping
  @ResponseStatus(HttpStatus.OK)
  public PaginatedResult<ChangeListRead> getChanges(
      @RequestParam(required = false) Long tenantId,
      @RequestParam(required = false) Long groupId,
      @RequestParam(required = false) Long ancestorId,
      @RequestParam(required = false) Long createdBy,
      @RequestParam(required = false) Long owner,
      @RequestParam(required = false) ChangeStatus status,
      @RequestParam(required = false) List<String> candidateGroups,
      @RequestParam(required = false) List<Long> candidateUsers,
      @RequestParam(required = false) String search,
      @RequestParam(required = false) String filter,
      @RequestParam(required = false) String sort,
      @RequestParam(required = false) Long pageSize,
      @RequestParam(required = false) Long pageNumber,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    Page<Change> changes =
        changeService.readAll(
            tenantId,
            groupId,
            ancestorId,
            createdBy,
            owner,
            status,
            candidateGroups,
            candidateUsers,
            search,
            filter,
            sort,
            pageSize,
            pageNumber);
    return changeMapper.paginatedChangesToPaginatedChangeListReads(changes);
  }

  @GetMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public ChangeRead getChange(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Change change = changeService.read(id, jwtDetails.getTenantId());
    change.setFiles(
        fileService.setUrls(change.getFiles().stream().toList()).stream()
            .collect(Collectors.toSet()));
    return changeMapper.changeToChangeRead(change);
  }

  @GetMapping("/{id}/deletable")
  @ResponseStatus(HttpStatus.OK)
  public Deletable isDeletable(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Change change = changeService.read(id, jwtDetails.getTenantId());
    Boolean deletable = changeService.checkDeletable(change);
    Deletable changeDeletable = new Deletable();
    changeDeletable.setDeletable(deletable);
    return changeDeletable;
  }

  @PutMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public ChangeRead updateChange(
      @PathVariable Long id,
      @RequestBody ChangeUpdate changeUpdate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    Change change = changeService.read(id, jwtDetails.getTenantId());
    changeMapper.updateChangeFromChangeUpdate(change, changeUpdate);
    return changeMapper.changeToChangeRead(
        changeService.read(changeService.update(change), jwtDetails.getTenantId()));
  }

  @DeleteMapping("/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteChange(@PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Change change = changeService.read(id, jwtDetails.getTenantId());
    changeService.delete(change);
  }

  @PostMapping("/{id}/cancel")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void cancelChange(@PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    Change change = changeService.read(id, jwtDetails.getTenantId());
    changeService.cancel(change);
  }

  @GetMapping("/{id}/pdf")
  @ResponseBody
  public void downloadPdf(
      @PathVariable Long id,
      @RequestParam(required = true) String timeZone,
      @AuthenticationPrincipal JwtDetails jwtDetails,
      HttpServletResponse response)
      throws IOException {
    Change change = changeService.read(id, jwtDetails.getTenantId());
    response.setContentType(MediaType.APPLICATION_PDF_VALUE);
    response.addHeader(
        HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=Change " + change.getSid() + ".pdf");
    changeService.generatePdf(change, timeZone, response.getOutputStream());
  }

  @GetMapping(value = "/{id}/history")
  @ResponseBody
  public List<ChangeChange> history(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    return changeHistoryService.getHistory(id, jwtDetails.getTenantId());
  }
}
