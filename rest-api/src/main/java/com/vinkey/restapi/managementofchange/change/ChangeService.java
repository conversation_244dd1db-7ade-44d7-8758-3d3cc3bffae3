package com.vinkey.restapi.managementofchange.change;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.vinkey.restapi.common.persistence.filter.FilterDecoder;
import com.vinkey.restapi.common.persistence.filter.SpecFilterCriteria;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom;
import com.vinkey.restapi.flowable.task.TaskService;
import com.vinkey.restapi.flowable.task.history.TaskHistory;
import com.vinkey.restapi.managementofchange.change.exception.ChangeLockedException;
import com.vinkey.restapi.managementofchange.change.exception.ChangeNotDeletableException;
import com.vinkey.restapi.managementofchange.change.exception.ChangeNotFoundException;
import com.vinkey.restapi.managementofchange.change.notification.event.ChangeAcceptedEvent;
import com.vinkey.restapi.managementofchange.change.notification.event.ChangeCanceledEvent;
import com.vinkey.restapi.managementofchange.change.notification.event.ChangeClosedEvent;
import com.vinkey.restapi.managementofchange.change.notification.event.ChangeImplementedEvent;
import com.vinkey.restapi.managementofchange.change.notification.event.ChangeImplementingEvent;
import com.vinkey.restapi.managementofchange.change.notification.event.ChangeRejectedEvent;
import com.vinkey.restapi.managementofchange.change.notification.event.ChangeUpdatedEvent;
import com.vinkey.restapi.managementofchange.checklist.Checklist;
import com.vinkey.restapi.managementofchange.checklist.ChecklistStatus;
import com.vinkey.restapi.managementofchange.checklist.answer.ChecklistAnswer;
import com.vinkey.restapi.managementofchange.checklist.answer.IssueStatus;
import com.vinkey.restapi.permittowork.workpermit.PDFException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.stream.Collectors;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceBuilder;
import org.jsoup.Jsoup;
import org.jsoup.helper.W3CDom;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.Context;

@Service
public class ChangeService {
  private final ChangeRepository changeRepository;
  private final RuntimeService runtimeService;
  private final ApplicationEventPublisher applicationEventPublisher;
  private final ITemplateEngine templateEngine;
  private final TaskService taskService;

  @Value("${pagination.defaultpageSize}")
  private Long pageSize = 20L;

  @Value("${pagination.defaultpageNumber}")
  private Long pageNumber = 0L;

  public ChangeService(
      ChangeRepository changeRepository,
      RuntimeService runtimeService,
      ApplicationEventPublisher applicationEventPublisher,
      ITemplateEngine templateEngine,
      TaskService taskService) {
    this.changeRepository = changeRepository;
    this.runtimeService = runtimeService;
    this.applicationEventPublisher = applicationEventPublisher;
    this.templateEngine = templateEngine;
    this.taskService = taskService;
  }

  @PostAuthorize(
      """
        hasRole('TENANT_ADMIN')
        OR @membershipService.hasPrivilege(returnObject.group.id, authentication.principal.userId, 'CHANGE_READ')
      """)
  public Change read(Long id, Long tenantId) {
    return changeRepository
        .findByIdAndTenantId(id, tenantId)
        .orElseThrow(() -> new ChangeNotFoundException(id));
  }

  @PreAuthorize(
      """
        hasRole('TENANT_ADMIN')
        or @membershipService.hasPrivilege(#groupId, authentication.principal.userId, 'CHANGE_READ')
        or @membershipService.hasPrivilege(#ancestorId, authentication.principal.userId, 'CHANGE_READ')
        or @membershipService.hasPrivilege(
          @filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "groupId"),
          authentication.principal.userId,
          'CHANGE_READ'
        )
        or @membershipService.hasPrivilege(
          @filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "ancestorId"),
          authentication.principal.userId,
          'CHANGE_READ'
        )
      """)
  public Page<Change> readAll(
      Long tenantId,
      Long groupId,
      Long ancestorId,
      Long createdBy,
      Long owner,
      ChangeStatus status,
      List<String> candidateGroups,
      List<Long> candidateUsers,
      String search,
      String filter,
      String sort,
      Long pageSize,
      Long pageNumber) {
    pageSize = pageSize == null ? this.pageSize : pageSize;
    pageNumber = pageNumber == null ? this.pageNumber : pageNumber;

    Sort sortBy = Sort.by(Sort.Direction.DESC, Change_.ID);
    if (sort != null && !sort.isEmpty()) {
      List<Sort.Order> sortOrders =
          com.vinkey.restapi.common.persistence.sort.SortDecoder.decode(sort, ChangeSortBy.class)
              .stream()
              .toList();
      if (!sortOrders.isEmpty()) {
        sortBy = Sort.by(sortOrders);
      }
    }

    Pageable pageable = PageRequest.of(pageNumber.intValue(), pageSize.intValue(), sortBy);
    ChangeSpecificationBuilder builder =
        new ChangeSpecificationBuilder()
            .tenant(tenantId)
            .group(groupId)
            .ancestorId(ancestorId)
            .createdBy(createdBy)
            .owner(owner)
            .status(status)
            .candidateGroups(candidateGroups)
            .candidateUsers(candidateUsers)
            .search(search);

    if (filter != null) {
      Queue<SpecFilterCriteria> filterSpecs = FilterDecoder.decode(filter);
      while (!filterSpecs.isEmpty()) {
        builder.withFilter(filterSpecs.poll());
      }
    }
    return changeRepository.findAll(builder.build(), pageable);
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#change.getGroup().getId(), authentication.principal.userId, 'CHANGE_CREATE')")
  public Long create(Change change) {
    Long tenantId = change.getTenant().getId();
    ProcessInstanceBuilder processInstanceBuilder =
        runtimeService
            .createProcessInstanceBuilder()
            .processDefinitionKey("changeProcess")
            .tenantId(tenantId.toString());

    ProcessInstance processInstance = processInstanceBuilder.start();
    String processInstanceId = processInstance.getProcessInstanceId();

    ProcessInstanceCustom processInstanceCustom = new ProcessInstanceCustom();
    processInstanceCustom.setId(processInstance.getId());
    processInstanceCustom.setProcessInstanceId(processInstanceId);
    change.setProcessInstance(processInstanceCustom);

    changeRepository.save(change);

    Map<String, Object> variables = new HashMap<String, Object>();
    variables.put("groupId", change.getGroup().getId());
    variables.put("id", change.getId());
    variables.put("sid", change.getSid());
    variables.put("name", change.getTitle());
    variables.put("created_by", change.getCreatedBy().getId());
    variables.put("ready", true);

    runtimeService.setVariables(processInstanceId, variables);
    return change.getId();
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#change.getGroup().getId(), authentication.principal.userId, 'CHANGE_UPDATE')")
  public Long update(Change change) {
    if (Boolean.TRUE.equals(change.getLocked())) {
      throw new ChangeLockedException(change.getId());
    }

    Change oldChange = read(change.getId(), change.getTenant().getId());

    Map<String, Object> variables = new HashMap<String, Object>();

    if (!Objects.equals(change.getTitle(), oldChange.getTitle())) {
      variables.put("name", change.getTitle());
    }

    runtimeService.setVariables(change.getProcessInstance().getId(), variables);

    changeRepository.save(change);
    applicationEventPublisher.publishEvent(new ChangeUpdatedEvent(change.getId()));
    return change.getId();
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#change.getGroup().getId(), authentication.principal.userId, 'CHANGE_DELETE')")
  public Boolean checkDeletable(Change change) {
    return !changeRepository.existsAnyRelationsById(change.getId());
  }

  @Transactional
  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#change.getGroup().getId(), authentication.principal.userId, 'CHANGE_DELETE')")
  public void delete(Change change) {
    if (!checkDeletable(change)) {
      throw new ChangeNotDeletableException(change.getId());
    }
    changeRepository.delete(change);
  }

  @Transactional
  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#change.getGroup().getId(), authentication.principal.userId, 'CHANGE_CANCEL')")
  public void cancel(Change change) {
    if (change.getChecklists() == null) {
      return;
    }

    cancelChecklists(change);
    runtimeService.deleteProcessInstance(change.getProcessInstance().getId(), "Change canceled");
    change.setLocked(true);
    change.setStatus(ChangeStatus.CANCELED);
    changeRepository.save(change);
    applicationEventPublisher.publishEvent(new ChangeCanceledEvent(change.getId()));
  }

  @Transactional
  public void reject(Long id, Long tenantId) {
    Change change = read(id, tenantId);
    cancelChecklists(change);
    change.setStatus(ChangeStatus.REJECTED);
    changeRepository.save(change);
    applicationEventPublisher.publishEvent(new ChangeRejectedEvent(change.getId()));
  }

  @Transactional
  public void implementing(Long id, Long tenantId) {
    Change change = read(id, tenantId);
    change.setStatus(ChangeStatus.IMPLEMENTING);
    changeRepository.save(change);
    applicationEventPublisher.publishEvent(new ChangeImplementingEvent(change.getId()));
  }

  @Transactional
  public void implement(Long id, Long tenantId) {
    Change change = read(id, tenantId);
    change.setStatus(ChangeStatus.IMPLEMENTED);
    changeRepository.save(change);
    applicationEventPublisher.publishEvent(new ChangeImplementedEvent(change.getId()));
  }

  @Transactional
  public void accept(Long id, Long tenantId) {
    Change change = read(id, tenantId);
    change.setStatus(ChangeStatus.ACCEPTED);
    changeRepository.save(change);
    applicationEventPublisher.publishEvent(new ChangeAcceptedEvent(change.getId()));
  }

  @Transactional
  public void close(Long id, Long tenantId) {
    Change change = read(id, tenantId);
    change.setStatus(ChangeStatus.CLOSED);
    changeRepository.save(change);
    applicationEventPublisher.publishEvent(new ChangeClosedEvent(change.getId()));
  }

  @Transactional
  public void lock(Long id, Long tenantId) {
    Change change = read(id, tenantId);
    change.setLocked(true);
    changeRepository.save(change);
  }

  @Transactional
  public Boolean isCloseable(Long id, Long tenantId) {
    Change change = read(id, tenantId);
    return change.isCloseable();
  }

  public void generatePdf(Change change, String timeZone, OutputStream out) {
    Context context = generatePdfContext(change, timeZone);
    String html = templateEngine.process("pdf/change.html", context);
    Document document = Jsoup.parse(html, "UTF-8");
    document.outputSettings().syntax(Document.OutputSettings.Syntax.xml);

    // take the copy of the stream and re-write it to an InputStream
    // try-with-resources here
    // putting the try block outside the Thread will cause the
    // PipedOutputStream resource to close before the Runnable finishes
    try (out) {
      PdfRendererBuilder builder = new PdfRendererBuilder();

      builder.withUri("change.pdf");
      builder.toStream(out);
      builder.withW3cDocument(new W3CDom().fromJsoup(document), "/");
      builder.run();
    } catch (IOException e) {
      // logging and exception handling should go here
      e.printStackTrace();
      throw new PDFException();
    }
  }

  private Context generatePdfContext(Change change, String timeZone) {
    List<TaskHistory> historyTasks =
        taskService.getHistory(change.getProcessInstance().getId(), change.getTenant().getId());
    Map<String, List<TaskHistory>> multiValueMap = new HashMap<>();
    historyTasks.stream()
        .forEach(
            task -> {
              if (multiValueMap.containsKey(task.getTaskDefinitionKey()))
                multiValueMap.get(task.getTaskDefinitionKey()).add(task);
              else
                multiValueMap.put(
                    task.getTaskDefinitionKey(), new ArrayList<>(Arrays.asList(task)));
            });

    List<TaskHistory> taskOnly =
        multiValueMap.values().stream()
            .filter(taskList -> !taskList.isEmpty())
            .map(
                tasks ->
                    Collections.max(
                        tasks, Comparator.comparing(task -> task.getEndTime().getTime())))
            .collect(Collectors.toList());

    if (!taskOnly.isEmpty())
      Collections.sort(taskOnly, Comparator.comparing(TaskHistory::getEndTime));

    Context context = new Context();
    context.setVariable("change", change);
    context.setVariable("timeZone", timeZone);
    context.setVariable("tasks", taskOnly);

    return context;
  }

  private void cancelChecklists(Change change) {
    if (change.getChecklists() == null) {
      return;
    }

    for (Checklist checklist : change.getChecklists()) {
      checklist.setStatus(ChecklistStatus.CANCELED);
      checklist.setLocked(true);

      for (ChecklistAnswer checklistAnswer : checklist.getAnswers()) {
        checklistAnswer.setLocked(true);
        checklistAnswer.setStatus(IssueStatus.CANCELED);
      }

      if (checklist.getProcessInstance() != null
          && checklist.getProcessInstance().getEndTime() == null) {
        runtimeService.deleteProcessInstance(
            checklist.getProcessInstance().getId(), "Change canceled");
      }
    }
  }

  public Boolean isReviewable(Long id, Long tenantId) {
    Change change = read(id, tenantId);
    return change.isReviewable();
  }

  public Boolean isCheckable(Long id, Long tenantId) {
    Change change = read(id, tenantId);
    return change.isCheckable();
  }

  public Boolean isImplementable(Long id, Long tenantId) {
    Change change = read(id, tenantId);
    return change.isImplementable();
  }
}
