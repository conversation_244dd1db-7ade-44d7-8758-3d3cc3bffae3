package com.vinkey.restapi.managementofchange.checklist;

import com.vinkey.restapi.common.persistence.filter.FilterDecoder;
import com.vinkey.restapi.common.persistence.filter.SpecFilterCriteria;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom;
import com.vinkey.restapi.managementofchange.change.Change;
import com.vinkey.restapi.managementofchange.change.ChangeStatus;
import com.vinkey.restapi.managementofchange.change.exception.ChangeLockedException;
import com.vinkey.restapi.managementofchange.checklist.answer.Answer;
import com.vinkey.restapi.managementofchange.checklist.answer.ChecklistAnswer;
import com.vinkey.restapi.managementofchange.checklist.answer.IssueStatus;
import com.vinkey.restapi.managementofchange.checklist.answer.exception.ChecklistAnswersInCompleteException;
import com.vinkey.restapi.managementofchange.checklist.answer.notification.event.IssueCanceledEvent;
import com.vinkey.restapi.managementofchange.checklist.answer.notification.event.IssueUpdatedEvent;
import com.vinkey.restapi.managementofchange.checklist.exception.ChecklistLockedException;
import com.vinkey.restapi.managementofchange.checklist.exception.ChecklistNotAbleToAddToRequestedChange;
import com.vinkey.restapi.managementofchange.checklist.exception.ChecklistNotDeletable;
import com.vinkey.restapi.managementofchange.checklist.exception.ChecklistNotFoundException;
import com.vinkey.restapi.managementofchange.checklist.notification.event.ChecklistResolvedEvent;
import com.vinkey.restapi.managementofchange.checklist.notification.event.ChecklistSubmittedEvent;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Queue;
import java.util.Set;
import java.util.stream.Collectors;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.ChangeActivityStateBuilder;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

@Service
public class ChecklistService {
  private final ChecklistRepository checklistRepository;
  private final RuntimeService runtimeService;
  private final ApplicationEventPublisher applicationEventPublisher;
  private final TaskService taskService;

  @Value("${pagination.defaultpageSize}")
  private Long pageSize = 20L;

  @Value("${pagination.defaultpageNumber}")
  private Long pageNumber = 0L;

  public ChecklistService(
      ChecklistRepository checklistRepository,
      RuntimeService runtimeService,
      ApplicationEventPublisher applicationEventPublisher,
      TaskService taskService) {
    this.checklistRepository = checklistRepository;
    this.runtimeService = runtimeService;
    this.applicationEventPublisher = applicationEventPublisher;
    this.taskService = taskService;
  }

  @PostAuthorize(
      """
      hasRole('TENANT_ADMIN')
      OR @membershipService.hasPrivilege(returnObject.group.id, authentication.principal.userId, 'CHANGE_READ')
    """)
  public Checklist read(Long id, Long tenantId) {
    Optional<Checklist> checklist = checklistRepository.findByIdAndTenantId(id, tenantId);
    if (checklist.isEmpty()) {
      throw new ChecklistNotFoundException(id);
    }
    return checklist.get();
  }

  @PreAuthorize(
      """
    hasRole('TENANT_ADMIN')
    or @membershipService.hasPrivilege(#groupId, authentication.principal.userId, 'CHANGE_READ')
    or @membershipService.hasPrivilege(#ancestorGroupId, authentication.principal.userId, 'CHANGE_READ')
    or @membershipService.hasPrivilege(
      @filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "groupId"),
      authentication.principal.userId,
      'CHANGE_READ'
    )
    or @membershipService.hasPrivilege(
      @filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "ancestorGroupId"),
      authentication.principal.userId,
      'CHANGE_READ'
    )
  """)
  public Page<Checklist> readAll(
      Long tenantId,
      Long groupId,
      Long ancestorGroupId,
      Long createdBy,
      Long assignee,
      ChecklistStatus status,
      Long changeId,
      String search,
      String filter,
      String sort,
      Long pageNumber,
      Long pageSize) {
    pageSize = pageSize == null ? this.pageSize : pageSize;
    pageNumber = pageNumber == null ? this.pageNumber : pageNumber;

    Sort sortBy = Sort.by(Sort.Direction.DESC, Checklist_.ID);
    if (sort != null && !sort.isEmpty()) {
      List<Sort.Order> sortOrders =
          com.vinkey.restapi.common.persistence.sort.SortDecoder.decode(
                  sort, ChecklistSortBy.class)
              .stream()
              .toList();
      if (!sortOrders.isEmpty()) {
        sortBy = Sort.by(sortOrders);
      }
    }

    Pageable pageable = PageRequest.of(pageNumber.intValue(), pageSize.intValue(), sortBy);

    ChecklistSpecificationBuilder builder =
        new ChecklistSpecificationBuilder()
            .distinct()
            .tenant(tenantId)
            .group(groupId)
            .ancestorId(ancestorGroupId)
            .createdBy(createdBy)
            .assignee(assignee)
            .status(status)
            .change(changeId)
            .search(search);

    if (filter != null) {
      Queue<SpecFilterCriteria> filterSpecs = FilterDecoder.decode(filter);
      while (!filterSpecs.isEmpty()) {
        builder.withFilter(filterSpecs.poll());
      }
    }
    return checklistRepository.findAll(builder.build(), pageable);
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#checklist.getGroup().getId(), authentication.principal.userId, 'CHANGE_CREATE')")
  @Transactional
  public Checklist create(Checklist checklist) {

    if (checklist.getChange() != null) {
      Change change = checklist.getChange();
      if (Objects.equals(change.getLocked(), true)) {
        throw new ChangeLockedException(change.getId());
      }

      if (change.getStatus().equals(ChangeStatus.REQUESTED)) {
        throw new ChecklistNotAbleToAddToRequestedChange();
      }
    }

    Boolean scheduled = checklist.getStatus().equals(ChecklistStatus.TODO);

    String tenantId = checklist.getTenant().getId().toString();

    ProcessInstance processInstance =
        runtimeService.startProcessInstanceByKeyAndTenantId("checklistProcess", tenantId);

    ProcessInstanceCustom processInstanceCustom = new ProcessInstanceCustom();
    processInstanceCustom.setId(processInstance.getId());
    processInstanceCustom.setProcessInstanceId(processInstance.getProcessInstanceId());
    checklist.setProcessInstance(processInstanceCustom);

    Map<String, Object> startUpVariables = new HashMap<String, Object>();

    for (ChecklistAnswer answer : checklist.getAnswers()) {

      if (scheduled) {
        answer.setStatus(IssueStatus.DRAFT);
      }

      if (!scheduled) {
        attachProcessToIssue(answer);
      }

      answer.setChange(checklist.getChange());
    }

    checklistRepository.saveAndFlush(checklist);

    if (scheduled) {
      startUpVariables.putAll(getScheduledVariables(checklist));
    }

    if (!scheduled) {
      for (ChecklistAnswer answer : checklist.getAnswers()) {
        runtimeService.setVariables(answer.getProcessInstance().getId(), getIssueVariables(answer));
      }
    }

    Checklist createdChecklist = read(checklist.getId(), checklist.getTenant().getId());

    startUpVariables.putAll(getVariables(createdChecklist));
    startUpVariables.put("allIssuesCompleted", !hasCompletableIssues(checklist));

    runtimeService.setVariables(processInstance.getProcessInstanceId(), startUpVariables);

    return checklist;
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#checklist.getGroup().getId(), authentication.principal.userId, 'CHANGE_UPDATE')")
  @Transactional
  public Checklist update(Checklist checklist) {

    if (checklist.getLocked()) {
      throw new ChecklistLockedException(checklist.getId());
    }

    if (checklist.getChange() != null) {
      Change change = checklist.getChange();
      if (Objects.equals(change.getLocked(), true)) {
        throw new ChangeLockedException(change.getId());
      }
    }

    Boolean scheduled = checklist.getStatus().equals(ChecklistStatus.TODO);
    Checklist oChecklist = read(checklist.getId(), checklist.getTenant().getId());

    if (scheduled) {
      if (!Objects.equals(oChecklist.getAssignee().getId(), checklist.getAssignee().getId())) {
        updateSubmitAssignee(checklist);
      }
      checklistRepository.save(checklist);
      return checklist;
    }

    Map<ChecklistAnswer, ChecklistAnswer> oldToNewMap =
        new HashMap<ChecklistAnswer, ChecklistAnswer>();

    for (ChecklistAnswer nChecklistAnswer : checklist.getAnswers()) {
      oChecklist.getAnswers().stream()
          .filter(oca -> oca.getId().equals(nChecklistAnswer.getId()))
          .findFirst()
          .ifPresent(oChecklistAnswer -> oldToNewMap.put(oChecklistAnswer, nChecklistAnswer));
    }

    Set<ChecklistAnswer> addedAnswers =
        oldToNewMap.entrySet().stream()
            .filter(
                e -> e.getKey().getAnswer() != Answer.YES && e.getValue().getAnswer() == Answer.YES)
            .map(Map.Entry::getValue)
            .collect(Collectors.toSet());
    Set<ChecklistAnswer> removedAnswers =
        oldToNewMap.entrySet().stream()
            .filter(
                e -> e.getKey().getAnswer() == Answer.YES && e.getValue().getAnswer() != Answer.YES)
            .map(Map.Entry::getValue)
            .collect(Collectors.toSet());
    Set<ChecklistAnswer> updatedAnswers =
        oldToNewMap.entrySet().stream()
            .filter(e -> !checklistAnswersEqual(e.getKey(), e.getValue()))
            .map(Map.Entry::getValue)
            .collect(Collectors.toSet());

    for (ChecklistAnswer checklistAnswer : addedAnswers) {
      String processInstanceId = checklistAnswer.getProcessInstance().getId();
      checklistAnswer.setLocked(false);
      checklistAnswer.setStatus(IssueStatus.ACTIVE);
      runtimeService.setVariables(processInstanceId, getIssueVariables(checklistAnswer));
    }

    for (ChecklistAnswer checklistAnswer : removedAnswers) {
      checklistAnswer.setLocked(true);
      checklistAnswer.setStatus(IssueStatus.CANCELED);

      List<String> variableNames = Arrays.asList("ready");
      runtimeService.removeVariablesLocal(
          checklistAnswer.getProcessInstance().getId(), variableNames);

      List<Execution> issueExecution =
          runtimeService
              .createExecutionQuery()
              .processInstanceId(checklistAnswer.getProcessInstance().getId())
              .list();

      String issueExecutionId =
          issueExecution.stream().filter(e -> e.getActivityId() != null).findFirst().get().getId();

      ChangeActivityStateBuilder issueChangeStateBuilder =
          runtimeService.createChangeActivityStateBuilder();
      issueChangeStateBuilder.processInstanceId(checklistAnswer.getProcessInstance().getId());
      issueChangeStateBuilder.moveExecutionToActivityId(issueExecutionId, "issueStarted");
      issueChangeStateBuilder.changeState();
    }

    for (ChecklistAnswer checklistAnswer : updatedAnswers) {
      if (checklistAnswer.getDescription() != null
          && checklistAnswer.getDescription() != null
          && !Objects.equals(checklistAnswer.getDescription(), checklistAnswer.getDescription())) {
        runtimeService.setVariable(
            checklistAnswer.getProcessInstance().getId(), "name", checklistAnswer.getDescription());
      }
      applicationEventPublisher.publishEvent(new IssueUpdatedEvent(checklistAnswer.getId()));
    }

    checklistRepository.saveAndFlush(checklist);
    Checklist updatedChecklist = read(checklist.getId(), checklist.getTenant().getId());

    if (removedAnswers.size() != 0) {
      sentIssueEndedSignal(checklist.getProcessInstance().getId());
    }

    return updatedChecklist;
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#checklist.getGroup().getId(), authentication.principal.userId, 'CHANGE_DELETE')")
  public Boolean checkDeletable(Checklist checklist) {
    return !checklistRepository.existsAnyRelationsById(checklist.getId());
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#checklist.getGroup().getId(), authentication.principal.userId, 'CHANGE_DELETE')")
  public void delete(Checklist checklist) {
    if (!checkDeletable(checklist)) {
      throw new ChecklistNotDeletable(checklist.getId());
    }
    checklistRepository.delete(checklist);
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#checklist.getGroup().getId(), authentication.principal.userId, 'CHANGE_CANCEL')  or #checklist.getCreatedBy().getId().equals(authentication.principal.userId)")
  @Transactional
  public void cancel(Checklist checklist) {
    checklist.setLocked(true);
    checklist.setStatus(ChecklistStatus.CANCELED);
    for (ChecklistAnswer answer : checklist.getAnswers()) {
      answer.setLocked(true);
      answer.setStatus(IssueStatus.CANCELED);
      applicationEventPublisher.publishEvent(new IssueCanceledEvent(answer.getId()));
    }
    runtimeService.deleteProcessInstance(
        checklist.getProcessInstance().getId(), "Checklist canceled by user");

    checklistRepository.save(checklist);
  }

  @Transactional
  public void lock(Long id, Long tenantId) {
    Checklist checklist = read(id, tenantId);
    checklist.setLocked(true);
    checklistRepository.save(checklist);
  }

  @Transactional
  public void resolve(Long id, Long tenantId) {
    Checklist checklist = read(id, tenantId);
    checklist.setStatus(ChecklistStatus.RESOLVED);

    for (ChecklistAnswer answer : checklist.getAnswers()) {
      answer.setStatus(IssueStatus.RESOLVED);
    }

    checklistRepository.save(checklist);
    applicationEventPublisher.publishEvent(new ChecklistResolvedEvent(id));
  }

  @Transactional
  public void submit(Long id, Long tenantId) {
    Checklist checklist = read(id, tenantId);
    checklist.setStatus(ChecklistStatus.ACTIVE);

    if (!answersValidCheck(checklist.getAnswers())) {
      throw new ChecklistAnswersInCompleteException();
    }

    for (ChecklistAnswer answer : checklist.getAnswers()) {
      answer.setStatus(IssueStatus.ACTIVE);
      attachProcessToIssue(answer);
      runtimeService.setVariables(answer.getProcessInstance().getId(), getIssueVariables(answer));
    }

    runtimeService.setVariable(
        checklist.getProcessInstance().getProcessInstanceId(),
        "allIssuesCompleted",
        !hasCompletableIssues(checklist));

    checklistRepository.save(checklist);

    applicationEventPublisher.publishEvent(new ChecklistSubmittedEvent(id));
  }

  @Transactional
  public void checkAllIssuesCompleted(Long id, Long tenantId) {
    Checklist checklist = read(id, tenantId);
    runtimeService.setVariable(
        checklist.getProcessInstance().getId(),
        "allIssuesCompleted",
        !hasCompletableIssues(checklist));
  }

  @Transactional
  public void sentIssueEndedSignal(String checklistProcessInstance) {
    if (checklistProcessInstance == null) {
      return;
    }

    runtimeService.signalEventReceived(
        "issueEndedSignal",
        runtimeService
            .createExecutionQuery()
            .signalEventSubscriptionName("issueEndedSignal")
            .processInstanceId(checklistProcessInstance)
            .singleResult()
            .getId());
  }

  private Boolean checklistAnswersEqual(
      ChecklistAnswer oChecklistAnswer, ChecklistAnswer nChecklistAnswer) {

    if (oChecklistAnswer != null && nChecklistAnswer == null
        || oChecklistAnswer == null && nChecklistAnswer != null) {
      return true;
    }

    if (oChecklistAnswer == null && nChecklistAnswer == null) {
      return false;
    }

    if (!Objects.equals(oChecklistAnswer.getAnswer(), nChecklistAnswer.getAnswer())) {
      return false;
    }

    if (!Objects.equals(oChecklistAnswer.getDescription(), nChecklistAnswer.getDescription())) {
      return false;
    }

    return true;
  }

  private Boolean hasCompletableIssues(Checklist checklist) {
    // ? if all issue's are resolved/removed (will result in the cancel status) or if there are no
    // issue's to begin with.
    Long nrOfNonIssues = checklist.getNoIsuessTotalCount();
    Long nrOfIssues = checklist.getIssuesTotalCount();
    Long nrOfIssuesResolved =
        checklist.getAnswers().stream()
            .filter(os -> os.getStatus().equals(IssueStatus.RESOLVED))
            .count();

    return !(nrOfIssuesResolved == nrOfIssues || nrOfNonIssues == checklist.getAnswers().size());
  }

  private Map<String, Object> getVariables(Checklist checklist) {
    Map<String, Object> variables = new HashMap<String, Object>();

    variables.put("groupId", checklist.getGroup().getId());
    variables.put("id", checklist.getId());
    variables.put("name", checklist.getChecklistTemplate().getName());
    variables.put("sid", checklist.getSid());
    variables.put("created_by", checklist.getCreatedBy().getId());
    variables.put("ready", true);

    return variables;
  }

  private Map<String, Object> getIssueVariables(ChecklistAnswer checklistAnswer) {
    Map<String, Object> variables = new HashMap<String, Object>();
    variables.put("id", checklistAnswer.getId());
    variables.put("groupId", checklistAnswer.getGroup().getId());
    variables.put("created_by", checklistAnswer.getCreatedBy().getId());
    variables.put(
        "checklistProcessInstance", checklistAnswer.getChecklist().getProcessInstance().getId());

    if (checklistAnswer.getSid() != null && checklistAnswer.getDescription() != null) {
      variables.put("sid", checklistAnswer.getSid());
      variables.put("name", checklistAnswer.getDescription());
    }

    if (checklistAnswer.getAnswer() == Answer.YES) {
      variables.put("ready", true);
    }

    return variables;
  }

  private Map<String, Object> getScheduledVariables(Checklist checklist) {
    Map<String, Object> variables = new HashMap<String, Object>();
    variables.put("scheduled", true);
    variables.put("scheduledAssignee", checklist.getAssignee().getId());
    return variables;
  }

  private void attachProcessToIssue(ChecklistAnswer answer) {
    ProcessInstance issueProcessInstance =
        runtimeService.startProcessInstanceByKeyAndTenantId(
            "issueProcess", answer.getTenant().getId().toString());

    ProcessInstanceCustom processInstanceCustom = new ProcessInstanceCustom();
    processInstanceCustom.setId(issueProcessInstance.getId());
    processInstanceCustom.setProcessInstanceId(issueProcessInstance.getProcessInstanceId());
    answer.setProcessInstance(processInstanceCustom);
  }

  private void updateSubmitAssignee(Checklist checklist) {
    if (checklist.getAssignee() != null) {
      Task task =
          taskService
              .createTaskQuery()
              .processInstanceId(checklist.getProcessInstance().getId())
              .active()
              .taskDefinitionKey("bpmnTask_5")
              .singleResult();

      // #TODO check if task is not null

      taskService.setAssignee(task.getId(), checklist.getAssignee().getId().toString());
    }
  }

  private Boolean answersValidCheck(Set<ChecklistAnswer> answers) {
    return answers.stream().allMatch(answer -> StringUtils.hasLength(answer.getDescription()));
  }
}
