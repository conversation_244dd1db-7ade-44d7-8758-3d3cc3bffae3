package com.vinkey.restapi.managementofchange.change;

import com.vinkey.restapi.common.persistence.sort.SortByEnum;

public enum ChangeSortBy implements SortByEnum {
  ID(Change_.ID),
  SID(Change_.SID),
  TITLE(Change_.TITLE),
  STATUS(Change_.STATUS),
  START_DATE(Change_.START_DATE),
  END_DATE(Change_.END_DATE),
  CREATION_DATE(Change_.CREATION_DATE),
  MODIFIED_DATE(Change_.MODIFIED_DATE);

  private final String field;

  ChangeSortBy(String field) {
    this.field = field;
  }

  @Override
  public String getField() {
    return field;
  }
}
